# Testing Guide for Envoy Dashboard Frontend

## Overview

This document provides comprehensive guidance for unit testing in the Envoy Dashboard Frontend application. The testing setup uses <PERSON><PERSON>'s default testing framework with <PERSON><PERSON> and <PERSON>.

## Testing Framework

- **Test Runner**: Karma
- **Testing Framework**: Jasmine
- **Browser**: Chrome (with Ch<PERSON>Headless for CI)
- **Coverage**: Istanbul

## Project Structure

```
src/
├── app/
│   ├── testing/
│   │   ├── test-utils.ts          # Common mock services and utilities
│   │   └── firebase-mocks.ts      # Firebase/Firestore mocks
│   ├── core/
│   │   └── services/
│   │       ├── auth/
│   │       ├── data/
│   │       ├── utils/
│   │       ├── chat/
│   │       ├── websocket/
│   │       └── notification/
│   ├── modules/
│   │   ├── auth/
│   │   ├── driver/
│   │   ├── reservation/
│   │   ├── vehicle/
│   │   └── invoicing/
│   └── shared/
├── test-setup.ts                  # Test configuration
└── karma.conf.js                  # Karma configuration
```

## Running Tests

### Development
```bash
# Run tests with watch mode
npm run test

# Run tests with watch mode (explicit)
npm run test:watch

# Run tests with coverage and watch
npm run test:coverage-watch
```

### CI/Production
```bash
# Run tests once (for CI)
npm run test:ci

# Run tests with coverage (for CI)
npm run test:coverage
```

## Test Coverage Goals

- **Statements**: 80%
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%

## Writing Tests

### Service Tests

#### Example: AuthService Test
```typescript
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AuthService } from './auth.service';
import { MockUtilsService } from '../../testing/test-utils';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let mockUtilsService: MockUtilsService;

  beforeEach(() => {
    mockUtilsService = new MockUtilsService();
    
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: UtilsService, useValue: mockUtilsService }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  it('should login successfully', async () => {
    const mockResponse = { status_code: 200, data: { token: 'mock-token' } };
    
    const loginPromise = service.login('user', 'pass');
    
    const req = httpMock.expectOne('login');
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
    
    const result = await loginPromise;
    expect(result).toEqual(mockResponse);
  });
});
```

### Component Tests

#### Example: Component Test
```typescript
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { MyComponent } from './my.component';
import { MockAuthService } from '../../testing/test-utils';

describe('MyComponent', () => {
  let component: MyComponent;
  let fixture: ComponentFixture<MyComponent>;
  let mockAuthService: MockAuthService;

  beforeEach(async () => {
    mockAuthService = new MockAuthService();

    await TestBed.configureTestingModule({
      declarations: [MyComponent],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: AuthService, useValue: mockAuthService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MyComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle user authentication', () => {
    const mockUser = createMockUser();
    mockAuthService.setUserData(mockUser);
    
    fixture.detectChanges();
    
    expect(component.isAuthenticated).toBe(true);
  });
});
```

## Mock Services

### Available Mock Services

1. **MockAuthService**: Mock authentication service
2. **MockDataService**: Mock HTTP data service
3. **MockUtilsService**: Mock utility service
4. **MockChatService**: Mock chat service
5. **MockWebsocketService**: Mock WebSocket service
6. **MockNotificationService**: Mock notification service

### Firebase Mocks

Use the Firebase mocks for testing Firestore operations:

```typescript
import { MockFirestore, mockCollectionData } from '../../testing/firebase-mocks';

// In your test setup
providers: [
  { provide: Firestore, useClass: MockFirestore }
]
```

## Test Utilities

### Helper Functions

```typescript
import { createMockUser, createMockReservation } from '../../testing/test-utils';

// Create mock data
const user = createMockUser({ type: 'ADMIN' });
const reservation = createMockReservation({ status: 'PENDING' });
```

## Best Practices

### 1. Test Structure
- Use `describe` blocks to group related tests
- Use `beforeEach` for setup
- Use `afterEach` for cleanup
- Keep tests focused and atomic

### 2. Mocking
- Mock external dependencies
- Use spy objects for method verification
- Mock HTTP requests with HttpTestingController
- Mock observables with `of()` and `throwError()`

### 3. Assertions
- Test both success and error scenarios
- Verify method calls with `toHaveBeenCalled()`
- Test observable emissions
- Verify component state changes

### 4. Async Testing
```typescript
// For Promises
it('should handle async operation', async () => {
  const result = await service.asyncMethod();
  expect(result).toBeDefined();
});

// For Observables
it('should emit values', (done) => {
  service.observable$.subscribe(value => {
    expect(value).toBeDefined();
    done();
  });
});
```

## Common Testing Patterns

### Testing HTTP Services
```typescript
it('should make GET request', () => {
  service.getData().subscribe(data => {
    expect(data).toBeDefined();
  });

  const req = httpMock.expectOne('/api/data');
  expect(req.request.method).toBe('GET');
  req.flush({ data: 'mock' });
});
```

### Testing Error Handling
```typescript
it('should handle errors', () => {
  service.getData().subscribe({
    error: (error) => {
      expect(error).toBeDefined();
    }
  });

  const req = httpMock.expectOne('/api/data');
  req.error(new ErrorEvent('Network error'));
});
```

### Testing Component Interactions
```typescript
it('should handle button click', () => {
  spyOn(component, 'onClick');
  
  const button = fixture.debugElement.nativeElement.querySelector('button');
  button.click();
  
  expect(component.onClick).toHaveBeenCalled();
});
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are properly mocked
2. **Async Issues**: Use `fakeAsync` and `tick()` for complex async scenarios
3. **DOM Testing**: Use `fixture.detectChanges()` after component changes
4. **Memory Leaks**: Always unsubscribe from observables in tests

### Debugging Tests
- Use `fdescribe` and `fit` to focus on specific tests
- Add `console.log` statements for debugging
- Check browser console for additional error information

## Continuous Integration

The test configuration supports CI environments:
- Uses ChromeHeadless for headless testing
- Generates coverage reports
- Fails build if coverage thresholds are not met

## Coverage Reports

Coverage reports are generated in the `coverage/` directory and include:
- HTML report for detailed analysis
- LCOV format for CI integration
- Text summary for quick overview

## Test Files Created

### Core Services
- ✅ `AuthService` - Authentication, login, logout, token management
- ✅ `DataService` - HTTP operations, error handling
- ✅ `UtilsService` - Token validation, device detection, utilities
- ✅ `ChatService` - Firestore operations, driver search
- ✅ `WebsocketService` - WebSocket connections, message handling
- ✅ `NotificationService` - Notification management

### Components
- ✅ `AppComponent` - Main application component
- ✅ `HomeComponent` - Home page component
- ✅ `ImportReservationsComponent` - Reservation import functionality
- ✅ `DriverListComponent` - Driver management list
- ✅ `ReservationListComponent` - Reservation management list

### Guards & Interceptors
- ✅ `AuthGuard` - Route protection and authentication
- ✅ `AuthInterceptor` - HTTP request/response interception

### Pipes
- ✅ `DateFormatPipe` - Date formatting utilities

### Resolvers
- ✅ `DriverPayDetailsResolver` - Driver payment data resolution
- ✅ `TripDetailsResolver` - Trip details data resolution

### Test Utilities
- ✅ `test-utils.ts` - Mock services and helper functions
- ✅ `firebase-mocks.ts` - Firebase/Firestore mocking utilities

## Test Coverage Summary

The test suite now covers:
- **Services**: 6 core services with comprehensive unit tests
- **Components**: 5 key components with integration tests
- **Guards**: Authentication and authorization guards
- **Interceptors**: HTTP request/response handling
- **Pipes**: Data transformation utilities
- **Resolvers**: Route data resolution
- **Utilities**: Mock services and test helpers

## Next Steps

To continue expanding test coverage:

1. **Add more component tests** for remaining modules
2. **Create integration tests** for complex workflows
3. **Add E2E tests** for critical user journeys
4. **Implement visual regression tests** for UI components
5. **Add performance tests** for critical operations
