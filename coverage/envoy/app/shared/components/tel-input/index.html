
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/shared/components/tel-input</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> app/shared/components/tel-input</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.08% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>35/97</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.02% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>10/37</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.21% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/33</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.45% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>35/96</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="tel-input.component.ts"><a href="tel-input.component.ts.html">tel-input.component.ts</a></td>
	<td data-value="36.08" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.08" class="pct low">36.08%</td>
	<td data-value="97" class="abs low">35/97</td>
	<td data-value="27.02" class="pct low">27.02%</td>
	<td data-value="37" class="abs low">10/37</td>
	<td data-value="21.21" class="pct low">21.21%</td>
	<td data-value="33" class="abs low">7/33</td>
	<td data-value="36.45" class="pct low">36.45%</td>
	<td data-value="96" class="abs low">35/96</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-15T07:32:51.445Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    