
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/shared/components/auto-complete-input</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> app/shared/components/auto-complete-input</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.61% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>26/73</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.44% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.23% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">34.72% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>25/72</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="auto-complete-input.component.ts"><a href="auto-complete-input.component.ts.html">auto-complete-input.component.ts</a></td>
	<td data-value="35.61" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 35%"></div><div class="cover-empty" style="width: 65%"></div></div>
	</td>
	<td data-value="35.61" class="pct low">35.61%</td>
	<td data-value="73" class="abs low">26/73</td>
	<td data-value="3.44" class="pct low">3.44%</td>
	<td data-value="29" class="abs low">1/29</td>
	<td data-value="19.23" class="pct low">19.23%</td>
	<td data-value="26" class="abs low">5/26</td>
	<td data-value="34.72" class="pct low">34.72%</td>
	<td data-value="72" class="abs low">25/72</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-15T07:32:51.445Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    