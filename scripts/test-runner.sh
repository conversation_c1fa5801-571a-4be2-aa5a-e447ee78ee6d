#!/bin/bash

# Test Runner Script for Envoy Dashboard Frontend
# This script provides various testing scenarios and utilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if npm is available
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi
}

# Function to install dependencies if needed
check_dependencies() {
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    npm run test:ci
    
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed!"
    else
        print_error "Unit tests failed!"
        exit 1
    fi
}

# Function to run tests with coverage
run_coverage_tests() {
    print_status "Running tests with coverage..."
    npm run test:coverage
    
    if [ $? -eq 0 ]; then
        print_success "Coverage tests completed!"
        print_status "Coverage report available in coverage/ directory"
        
        # Open coverage report if on macOS
        if [[ "$OSTYPE" == "darwin"* ]]; then
            print_status "Opening coverage report..."
            open coverage/envoy/index.html
        fi
    else
        print_error "Coverage tests failed!"
        exit 1
    fi
}

# Function to run specific test file
run_specific_test() {
    local test_file=$1
    if [ -z "$test_file" ]; then
        print_error "Please provide a test file path"
        exit 1
    fi
    
    print_status "Running specific test: $test_file"
    npx ng test --include="$test_file" --watch=false --browsers=ChromeHeadless
}

# Function to run tests for specific module
run_module_tests() {
    local module=$1
    if [ -z "$module" ]; then
        print_error "Please provide a module name (e.g., auth, driver, reservation)"
        exit 1
    fi
    
    print_status "Running tests for module: $module"
    npx ng test --include="**/modules/$module/**/*.spec.ts" --watch=false --browsers=ChromeHeadless
}

# Function to run service tests only
run_service_tests() {
    print_status "Running service tests..."
    npx ng test --include="**/services/**/*.spec.ts" --watch=false --browsers=ChromeHeadless
}

# Function to run component tests only
run_component_tests() {
    print_status "Running component tests..."
    npx ng test --include="**/components/**/*.spec.ts" --watch=false --browsers=ChromeHeadless
}

# Function to run guard and interceptor tests
run_guard_interceptor_tests() {
    print_status "Running guard and interceptor tests..."
    npx ng test --include="**/guards/**/*.spec.ts" --include="**/interceptors/**/*.spec.ts" --watch=false --browsers=ChromeHeadless
}

# Function to run pipe tests
run_pipe_tests() {
    print_status "Running pipe tests..."
    npx ng test --include="**/pipes/**/*.spec.ts" --watch=false --browsers=ChromeHeadless
}

# Function to watch tests during development
watch_tests() {
    print_status "Starting test watcher..."
    npm run test:watch
}

# Function to lint test files
lint_tests() {
    print_status "Linting test files..."
    npx eslint "src/**/*.spec.ts" --fix
    
    if [ $? -eq 0 ]; then
        print_success "Test files linted successfully!"
    else
        print_warning "Some linting issues found"
    fi
}

# Function to generate test coverage report
generate_coverage_report() {
    print_status "Generating detailed coverage report..."
    npm run test:coverage
    
    # Generate additional reports if tools are available
    if command -v lcov &> /dev/null; then
        print_status "Generating LCOV report..."
        lcov --summary coverage/envoy/lcov.info
    fi
}

# Function to clean test artifacts
clean_test_artifacts() {
    print_status "Cleaning test artifacts..."
    rm -rf coverage/
    rm -rf .nyc_output/
    print_success "Test artifacts cleaned!"
}

# Function to show test statistics
show_test_stats() {
    print_status "Test Statistics:"
    
    # Count test files
    local spec_files=$(find src -name "*.spec.ts" | wc -l)
    echo "  Test files: $spec_files"
    
    # Count test cases (approximate)
    local test_cases=$(grep -r "it(" src --include="*.spec.ts" | wc -l)
    echo "  Test cases: $test_cases"
    
    # Count describe blocks
    local test_suites=$(grep -r "describe(" src --include="*.spec.ts" | wc -l)
    echo "  Test suites: $test_suites"
}

# Function to validate test setup
validate_test_setup() {
    print_status "Validating test setup..."
    
    # Check if karma.conf.js exists
    if [ ! -f "karma.conf.js" ]; then
        print_error "karma.conf.js not found!"
        exit 1
    fi
    
    # Check if test setup file exists
    if [ ! -f "src/test-setup.ts" ]; then
        print_error "src/test-setup.ts not found!"
        exit 1
    fi
    
    # Check if test utilities exist
    if [ ! -f "src/app/testing/test-utils.ts" ]; then
        print_error "src/app/testing/test-utils.ts not found!"
        exit 1
    fi
    
    print_success "Test setup validation passed!"
}

# Function to show help
show_help() {
    echo "Test Runner for Envoy Dashboard Frontend"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  unit                    Run all unit tests"
    echo "  coverage                Run tests with coverage report"
    echo "  watch                   Watch tests during development"
    echo "  specific <file>         Run specific test file"
    echo "  module <name>           Run tests for specific module"
    echo "  services                Run service tests only"
    echo "  components              Run component tests only"
    echo "  guards                  Run guard and interceptor tests"
    echo "  pipes                   Run pipe tests only"
    echo "  lint                    Lint test files"
    echo "  clean                   Clean test artifacts"
    echo "  stats                   Show test statistics"
    echo "  validate                Validate test setup"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 unit                                    # Run all unit tests"
    echo "  $0 coverage                                # Run with coverage"
    echo "  $0 specific auth.service.spec.ts           # Run specific test"
    echo "  $0 module auth                             # Run auth module tests"
    echo "  $0 services                                # Run service tests"
}

# Main script logic
main() {
    check_npm
    check_dependencies
    
    case "${1:-help}" in
        "unit")
            run_unit_tests
            ;;
        "coverage")
            run_coverage_tests
            ;;
        "watch")
            watch_tests
            ;;
        "specific")
            run_specific_test "$2"
            ;;
        "module")
            run_module_tests "$2"
            ;;
        "services")
            run_service_tests
            ;;
        "components")
            run_component_tests
            ;;
        "guards")
            run_guard_interceptor_tests
            ;;
        "pipes")
            run_pipe_tests
            ;;
        "lint")
            lint_tests
            ;;
        "clean")
            clean_test_artifacts
            ;;
        "stats")
            show_test_stats
            ;;
        "validate")
            validate_test_setup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
