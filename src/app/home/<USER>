import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';

import { HomeComponent } from './home.component';
import { AuthService } from '@modules/auth/services/auth.service';
import { UtilsService, DataService, WebsocketService } from '@core/services';
import { MockAuthService, MockUtilsService } from '../testing/test-utils';

describe('HomeComponent', () => {
  let component: HomeComponent;
  let fixture: ComponentFixture<HomeComponent>;
  let mockAuthService: MockAuthService;
  let mockUtilsService: MockUtilsService;

  beforeEach(async () => {
    mockAuthService = new MockAuthService();
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      declarations: [HomeComponent],
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MatSidenavModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatListModule
      ],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: UtilsService, useValue: mockUtilsService },
        {
          provide: DataService,
          useValue: jasmine.createSpyObj('DataService', ['get', 'post', 'put', 'delete'])
        },
        {
          provide: WebsocketService,
          useValue: jasmine.createSpyObj('WebsocketService', ['connect', 'disconnect', 'emit'])
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeComponent);
    component = fixture.componentInstance;

    // Setup default mock returns
    mockUtilsService.isMobile.and.returnValue(false);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component).toBeDefined();
  });
});
