.live-container{
  height: 93vh;
}
.live-sidenav {
  width: 470px;
}
map-info-window{
  min-width: 400px;
  display: flex;
  flex-direction: column;
}
::ng-deep .mapboxgl-popup-content {
  width: 320px;
}
h2.trip-title {
  color: #007CC4;
  font-size: 16px;
  margin: 5px 0 10px 0;
}
.trip-customer-driver {
  display: flex;
  justify-content: space-between;
}
map-info-window hr {
  font-size: 1px;
  border-width: 0;
  height: 2px;
  background-color: #DFE9F5;
  width: 100%;
  margin-bottom: 15px;
}
map-info-window span {
  font-size: 14px;
  color: #808FA4;
}
.sidebar-title h2{
  color:#007CC4;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
}
.sidebar-title span{
  color: #808FA4;
  font-weight: 500;
  font-size: 14px;
  line-height: 19px;
  padding-left: 34px;
}
.sidebar-container {
  display: flex;
  flex-direction: row-reverse;
}
.sidebar-title {
  display: flex;
  flex-direction: row;
  padding: 20px;
}
.sidebar-title h2 {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 16px;
}
.sidebar-current-active, .sidebar-car-types {
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.sidebar-current-active h2,.sidebar-car-types h2 {
  font-weight: 700;
  font-size: 14px;
  line-height: 17px;
  color: #363F45;
}
.sidebar-car-types h1 {
  font-weight: 700;
  font-size: 24px;
  line-height: 29px;
  color: #007CC4;
}
.sidebar-current-active span,.sidebar-car-types span {
  font-weight: 700;
  font-size: 12px;
  line-height: 15px;
  color: #808FA4;
}
h1 {
  &.total {
    font-weight: 700;
    font-size: 24px;
    line-height: 29px;
    color: #363F45;
  }
  &.en-route {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #FF9500;
  }
  &.on-board {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #FF9500;
  }
  &.on-location {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #007CC4;
  }
  &.booked {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #6c6c6c;
  }
}
.flex-row{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 12px;
}
.flex-row div{
  text-align: center;
  min-width: 25%;

}
.match-parent {
  width: 100%;
  height: 100%;
}
:host .mapboxgl-popup {
  max-width: 400px;
  font: 12px/20px 'Helvetica Neue', Arial, Helvetica, sans-serif;
}
mgl-map {
  height: 100%;
  width: 100%;
}
.toggle-sidenav {
  position: absolute;
  top: 40px;
  z-index: 9999999999;
  margin-left: -35px;
  border-radius: 100%;
  padding: 0;
  min-width: auto;
  height: auto;
  width: 25px;
  margin-top:10px;
}
.toggle-sidenav.false {
  transform: rotate(180deg);
  margin-left: -3px;
  background-color: #fff;
  margin-top:10px;
}
.toggle-sidenav.mat-mdc-button>.mat-icon {
  line-height: 40px;
  margin: 0 0 3px 0;
  width: 20px;
  height: 25px!important;
  padding: 0;
}
::ng-deep {
  .mat-drawer-content {
    overflow: visible!important;
  }
  .mat-drawer.mat-drawer-side{
    z-index: auto!important;
  }
  .custom-menu-filter {
    top: -37px;
    left: 150px;
  }
  .custom-menu-filter.selected{
    left:178px
  }
  .custom-menu-filter-vehicle {
    top: -37px;
    left: 130px;
  }
  .custom-menu-filter.false{
    top:auto!important;
    left:auto!important;
  }
  .custom-menu-filter-vehicle.false{
    top:auto!important;
    left:auto!important;
  }
  .bg-button.true > * {
    color: #fff!important;
  }
}
.filter-side, .filter-search{
  position: absolute;
  z-index:9;
}
.close {
  font-weight: 500;
  font-size: 14px;
  line-height: 19px;
  color: #808FA4;
  display: flex;
  align-items: center;
}
.filter-search{
  right: 20px;
  top: 20px;
}
.filter-button{
  min-width: 130px;
  margin: 10px;
}
.sidebar-title .sidebar-title-icon {
  flex-direction: column;
  align-items: baseline;
}
.sidebar-title mat-icon {
  margin-top: 5px;
}
.flex-row div:after {
  content: " ";
  height: 50px;
  width: 0;
  border-left: 1px solid;
  margin-top: -60px;
  display: block;
  margin-left: 2px;
  color: #DFE9F5;
}
.flex-row div:first-child:after {
  display: none;
}
.filter-side.false {
  display: flex;
}
.filter-button {
  background-color: #E6F4FC!important;
  border: 1px solid #007CC4;
  border-radius: 25px;
  color: #007CC4!important;
  width: auto !important;
}
.box-border.OnLocation, .box-border.En_route{
  border-color:#FFF0E0;
  background-color: #FFF0E0;
  color: #FF9500 !important;
}
.box-border.Onboarded{
  border-color:#007CC4;
  background-color: #007CC4;
  color: #ffffff !important;
}
.box-border.Dropped{
  border-color:#34C759;
  background-color: #34C759;
  color: #ffffff !important;
}
.col-9 span{
  font-size: 10px;
}
.mat-divider {
  border-top-color: #DFE9F5;
}
.ml24 {
  margin-left: 24px;
}
span.shadow-background {
  background-color: #307cc4;
  color: #fff;
  padding: 0 5px;
  border-radius: 5px;
}
button.mat-mdc-menu-item.Booked.true {
  background-color: #6c6c6c;
  border-radius: 2px;
}
button.mat-mdc-menu-item.Dropped.true {
  background-color: #34C759;
  border-radius: 2px;
}
button.mat-mdc-menu-item.OnLocation.true span{
  color:#fff!important;
}
button.mat-mdc-menu-item.En_route.true  {
  background-color:#FF9500;
  border-radius: 2px;
}
button.mat-mdc-menu-item.OnLocation.true, button.mat-mdc-menu-item.Onboarded.true {
  background-color: rgba(0,124,196 ,0.5);
  border-radius: 2px;
}

.trip-name {
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #007CC4;
}

.preferences-list {
  font-weight: 500;
  font-size: 14px;
  line-height: 19px;
  color: #627186;
}

.point-name {
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #363F45;
}

.point-address {
  font-weight: 500;
  line-height: 19px;
  color: #808FA4;
}
.date-title {
  font-weight: 500;
  line-height: 19px;
  color: #808FA4;
}
.date-value {
  font-weight: 500;
  font-size: 11px;
  line-height: 19px;
  color: #363F45;
}

.actions-log {
  display: flex;
  justify-content: space-between;
  span {
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    color: #808FA4;
  }
}
.active-trip-icon:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  border-left: 3px dotted #808FA4;
  margin-top: 25px;
  height: 70px;
}
.no-dots .active-trip-icon:after {
  display: none;
}
.waypoint-item{
  margin-bottom: 10px;
}
.table.border .col-12:last-child .active-trip-icon:after {
  display: none;
}
.active-trip-icon {
  position: relative;
  overflow: visible;
}
::ng-deep {
  .filter-button.opened-true {
    background-color: #007CC4!important;
    color: #fff!important;
  }
  .filter-status-.opened-false {
    background-color: #fff!important;
    color: #000!important;
  }
  .bg-button.true {
    background-color: #1a7cc4b0;
  }
}
.filter-button.reset-filter {
  color: #FF9500!important;
  background: #FFF0E0!important;
}
::ng-deep canvas.mapboxgl-canvas {
  width: 100%!important;
}
.cursor{
  cursor: pointer;
}
::ng-deep{
  .PICKUP{
    max-width: 50px;
    margin-top: -20px;
    margin-right: -15px;
  }
  .DROPOFF {
    max-width: 50px;
    margin-right: -25px;
    margin-top: -40px;
  }
}
