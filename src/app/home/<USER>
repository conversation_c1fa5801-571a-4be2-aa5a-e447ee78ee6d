export class MarkerModel {
  position: { lat: number, lng: number };
  title: string;
  data: any;
  options: { icon:string };

  constructor(data: any) {
    this.position = data.position;
    this.title = data.title;
    this.data = data.data;
    this.options = data.options;
  }
}
export interface MapDirectionsResponse {
  //status: google.maps.DirectionsStatus;
  //result?: google.maps.DirectionsResult;
}
