import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { of, throwError } from 'rxjs';

import { SignInComponent } from './sign-in.component';
import { DataService, UtilsService } from '@core/services';
import { TwoFactorComponent } from '../two-factor/two-factor.component';

describe('SignInComponent', () => {
  let component: SignInComponent;
  let fixture: ComponentFixture<SignInComponent>;
  let dataService: jasmine.SpyObj<DataService>;
  let utilsService: jasmine.SpyObj<UtilsService>;
  let dialog: jasmine.SpyObj<MatDialog>;

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['post']);
    const utilsServiceSpy = jasmine.createSpyObj('UtilsService', ['openSnackBar']);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open', 'closeAll']);

    await TestBed.configureTestingModule({
      declarations: [SignInComponent],
      imports: [
        ReactiveFormsModule,
        MatDialogModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: DataService, useValue: dataServiceSpy },
        { provide: UtilsService, useValue: utilsServiceSpy },
        { provide: MatDialog, useValue: dialogSpy }
      ]
    }).compileComponents();

    dataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    utilsService = TestBed.inject(UtilsService) as jasmine.SpyObj<UtilsService>;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SignInComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.invalidCredentials).toBeFalse();
    expect(component.maximumAllowedAttempts).toBeFalse();
    expect(component.loginFormIsValid).toBeFalse();
    expect(component.hide).toBeTrue();
  });

  it('should initialize form with required validators', () => {
    const form = component.loginForm;
    expect(form.get('phone')).toBeTruthy();
    expect(form.get('password')).toBeTruthy();
    expect(form.get('phone')?.hasValidator(Validators.required)).toBeTrue();
    expect(form.get('password')?.hasValidator(Validators.required)).toBeTrue();
  });

  it('should update loginFormIsValid when form values change', () => {
    const form = component.loginForm;
    
    // Test with empty values
    form.patchValue({ phone: '', password: '' });
    expect(component.loginFormIsValid).toBeFalse();

    // Test with valid values
    form.patchValue({ phone: '1234567890', password: 'password123' });
    expect(component.loginFormIsValid).toBeTrue();
  });

  it('should handle successful login submission', fakeAsync(() => {
    const mockResponse = { status_code: 200, data: { message: 'Success' } };
    dataService.post.and.returnValue(Promise.resolve(mockResponse));
    dialog.open.and.returnValue({
      afterClosed: () => of('success')
    } as any);

    component.loginForm.patchValue({
      phone: '1234567890',
      password: 'password123'
    });

    component.onSubmit();
    tick();

    expect(dataService.post).toHaveBeenCalledWith('admin/login/send_verify', {
      phone_number: '1234567890',
      password: 'password123'
    });
    expect(dialog.open).toHaveBeenCalledWith(TwoFactorComponent, {
      panelClass: 'envoy-dialog',
      disableClose: true,
      width: '550px',
      data: { loginForm: component.loginForm }
    });
  }));

  it('should handle failed login submission', fakeAsync(() => {
    const mockResponse = { status_code: 400, data: { message: 'Invalid credentials' } };
    dataService.post.and.returnValue(Promise.resolve(mockResponse));

    component.loginForm.patchValue({
      phone: '1234567890',
      password: 'wrongpassword'
    });

    component.onSubmit();
    tick();

    expect(utilsService.openSnackBar).toHaveBeenCalledWith(
      'Invalid credentials',
      5000,
      'error'
    );
  }));

  it('should handle login submission error', fakeAsync(() => {
    dataService.post.and.returnValue(Promise.reject('Network error'));

    component.loginForm.patchValue({
      phone: '1234567890',
      password: 'password123'
    });

    component.onSubmit();
    tick();

    expect(utilsService.openSnackBar).toHaveBeenCalled();
  }));

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = spyOn(component['_subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
