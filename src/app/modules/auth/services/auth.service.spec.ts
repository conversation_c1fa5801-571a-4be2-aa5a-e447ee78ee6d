import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { AuthService } from './auth.service';
import { UtilsService } from '@core/services';
import { User } from '@modules/auth/models/user.model';
import { createMockUser, createMockPermission, MockUtilsService } from '../../../testing/test-utils';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockUtilsService: MockUtilsService;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    mockUtilsService = new MockUtilsService();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: Router, useValue: routerSpy },
        { provide: UtilsService, useValue: mockUtilsService }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('login', () => {
    it('should make POST request to login endpoint', async () => {
      const username = 'testuser';
      const password = 'testpass';
      const mockResponse = { status_code: 200, data: { token: 'mock-token' } };

      const loginPromise = service.login(username, password);

      const req = httpMock.expectOne('login');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ username, password });
      req.flush(mockResponse);

      const result = await loginPromise;
      expect(result).toEqual(mockResponse);
    });

    it('should handle login error', async () => {
      const username = 'testuser';
      const password = 'wrongpass';

      const loginPromise = service.login(username, password);

      const req = httpMock.expectOne('login');
      req.error(new ErrorEvent('Network error'), { status: 401 });

      const result = await loginPromise;
      expect(result.hasError).toBe(true);
      expect(result['status-code']).toBe(401);
    });
  });

  describe('logout', () => {
    it('should clear auth data and navigate to home', async () => {
      await service.logout();

      expect(mockUtilsService.clearAuth).toHaveBeenCalled();
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/home']);
    });
  });

  describe('user data management', () => {
    it('should set and get user data', () => {
      const mockUser = createMockUser();

      service.setUserData(mockUser);

      expect(service.getUserData()).toEqual(jasmine.any(User));
      expect(localStorage.getItem('userId')).toBe(JSON.stringify(mockUser.id));
    });

    it('should emit user data changes', (done) => {
      const mockUser = createMockUser();

      service.userData.subscribe(user => {
        if (user) {
          expect(user).toEqual(jasmine.any(User));
          done();
        }
      });

      service.setUserData(mockUser);
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when refresh token is valid', () => {
      localStorage.setItem('refresh', 'valid-token');
      mockUtilsService.isValidToken.and.returnValue(true);

      expect(service.isAuthenticated()).toBe(true);
    });

    it('should return false when refresh token is invalid', () => {
      localStorage.setItem('refresh', 'invalid-token');
      mockUtilsService.isValidToken.and.returnValue(false);

      expect(service.isAuthenticated()).toBe(false);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token when valid refresh token exists', (done) => {
      localStorage.setItem('refresh', 'valid-refresh-token');
      mockUtilsService.isValidToken.and.returnValue(true);

      service.refreshToken().subscribe(result => {
        expect(result.access).toBe('new-access-token');
        done();
      });

      const req = httpMock.expectOne('api/token/refresh');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ refresh: 'valid-refresh-token' });
      req.flush({ access: 'new-access-token' });
    });

    it('should throw error when refresh token is invalid', (done) => {
      localStorage.setItem('refresh', 'invalid-refresh-token');
      mockUtilsService.isValidToken.and.returnValue(false);

      service.refreshToken().subscribe({
        error: (error) => {
          expect(error.message).toBe('Invalid or Expired Token');
          done();
        }
      });
    });
  });

  describe('initializeAuthUser', () => {
    it('should return userId when access token is valid', async () => {
      localStorage.setItem('token', 'valid-token');
      localStorage.setItem('userId', '123');
      mockUtilsService.isValidToken.and.returnValue(true);

      const result = await service.initializeAuthUser();
      expect(result).toBe('123');
    });

    it('should refresh token and return userId when access token expired but refresh token valid', async () => {
      localStorage.setItem('token', 'expired-token');
      localStorage.setItem('refresh', 'valid-refresh-token');
      localStorage.setItem('userId', '123');
      mockUtilsService.isValidToken.and.returnValues(false, true);

      const initPromise = service.initializeAuthUser();

      const req = httpMock.expectOne('api/token/refresh');
      req.flush({ access: 'new-access-token' });

      const result = await initPromise;
      expect(result).toBe('123');
      expect(mockUtilsService.saveAuthToken).toHaveBeenCalledWith('new-access-token');
    });

    it('should return null when both tokens are invalid', async () => {
      localStorage.setItem('token', 'expired-token');
      localStorage.setItem('refresh', 'expired-refresh-token');
      mockUtilsService.isValidToken.and.returnValue(false);

      const result = await service.initializeAuthUser();
      expect(result).toBeNull();
    });
  });

  describe('redirectAfterAuth', () => {
    it('should redirect admin to live reservations', () => {
      const adminUser = createMockUser({ type: 'ADMIN' });
      service.setUserData(adminUser);

      service.redirectAfterAuth();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/reservations/live']);
    });

    it('should redirect user with reservation permissions to live reservations', () => {
      const user = createMockUser({
        type: 'USER',
        user_permissions: [
          createMockPermission('admin/reservations', 'Reservation Management')
        ]
      });
      service.setUserData(user);

      service.redirectAfterAuth();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/reservations/live']);
    });

    it('should redirect user with driver permissions to drivers page', () => {
      const user = createMockUser({
        type: 'USER',
        user_permissions: [
          createMockPermission('admin/drivers', 'Driver Management')
        ]
      });
      service.setUserData(user);

      service.redirectAfterAuth();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/drivers']);
    });

    it('should redirect to settings when no specific permissions found', () => {
      const user = createMockUser({
        type: 'USER',
        user_permissions: []
      });
      service.setUserData(user);

      service.redirectAfterAuth();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/settings']);
    });
  });
});
