import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { VehicleDetailsComponent } from './vehicle-details.component';
import { VehicleService } from '@modules/vehicle/services/vehicle.service';
import { UtilsService } from '@core/services';
import { VehicleModel } from '@modules/vehicle/models';
import { EVehicleStatus } from '@modules/vehicle/models/vehicle-status.enum';
import { VEHICLE_STATUS, VEHICLE_DOC_TYPES } from '@modules/vehicle/constants';

describe('VehicleDetailsComponent', () => {
  let component: VehicleDetailsComponent;
  let fixture: ComponentFixture<VehicleDetailsComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockVehicleService: jasmine.SpyObj<VehicleService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockUtils: jasmine.SpyObj<UtilsService>;

  const createMockVehicle = (id: number): VehicleModel => {
    return new VehicleModel({
      id,
      is_leased: 0,
      code: 'V123',
      capacity: 4,
      wheelchair_capacity: 0,
      type: 'sedan',
      make: 'Toyota',
      model: 'Camry',
      year: '2020',
      color: 'black',
      license: 'ABC123',
      status: EVehicleStatus.free,
      service_area: {
        id: 1,
        name: 'LA',
        address: '123 Main St',
        location: 'Downtown',
        status: true,
        zone: {}
      },
      location: 'Downtown',
      driver_status: 'available',
      miles_count: 1000,
      has_assigned_drivers: false,
      oil_level: 100,
      oil_due: 5000,
      mileage_due: 10000,
      tire_size: '205/55R16',
      vin: '1HGCM82633A123456',
      description: 'Test vehicle',
      trips_count: 0,
      vehicle_type: { id: 1, name: 'Sedan' },
      recent_trips_completed: [],
      documents: [],
      created: '2024-01-01',
      oil_change_every: '5000',
      odometer_reading: 1000,
      is_air_filter_clean: true,
      condition_checks: [],
      photos: []
    });
  };

  beforeEach(async () => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockRoute = jasmine.createSpyObj('ActivatedRoute', [], {
      data: of({ vehicle: createMockVehicle(123) }),
      parent: {}
    });
    mockVehicleService = jasmine.createSpyObj('VehicleService', ['updateStatus']);
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockUtils = jasmine.createSpyObj('UtilsService', []);

    await TestBed.configureTestingModule({
      declarations: [VehicleDetailsComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockRoute },
        { provide: VehicleService, useValue: mockVehicleService },
        { provide: MatDialog, useValue: mockDialog },
        { provide: UtilsService, useValue: mockUtils }
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(VehicleDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with vehicle data', () => {
    expect(component.vehicle).toBeTruthy();
    expect(component.vehicle.id).toBe(123);
    expect(component.vehicleStatus).toBe(EVehicleStatus.free);
  });

  it('should update vehicle status', async () => {
    const newStatus = EVehicleStatus.needRepair;
    component.vehicleStatus = newStatus;
    mockVehicleService.updateStatus.and.returnValue(Promise.resolve());

    await component.updateVehicleStatus();

    expect(mockVehicleService.updateStatus).toHaveBeenCalledWith(123, newStatus);
    expect(component.vehicle.status).toBe(newStatus);
  });

  it('should close side nav', () => {
    component.closeSideNav();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      [{ outlets: { sidenav: null } }],
      { relativeTo: mockRoute.parent }
    );
  });

  it('should assign vehicle to driver', () => {
    const mockDialogRef = {
      afterClosed: () => of(true)
    } as unknown as MatDialogRef<any>;

    mockDialog.open.and.returnValue(mockDialogRef);

    component.assignToDriver();

    expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
      width: '750px',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { vehicleId: 123 }
    });
    expect(component.vehicle.has_assigned_drivers).toBe(true);
  });
});
