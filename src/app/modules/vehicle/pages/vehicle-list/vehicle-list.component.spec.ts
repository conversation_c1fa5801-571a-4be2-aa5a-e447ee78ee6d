import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { VehicleListComponent } from './vehicle-list.component';
import { EnvoyAdvancedList } from '@shared/classes';
import { VehicleModel } from '@modules/vehicle/models';
import { AssignToDriverComponent } from '@modules/vehicle/components/assign-to-driver/assign-to-driver.component';
import { VehicleDocumentsComponent } from '@modules/vehicle/components/vehicle-documents/vehicle-documents.component';
import { EVehicleStatus } from '@modules/vehicle/models/vehicle-status.enum';

describe('VehicleListComponent', () => {
  let component: VehicleListComponent;
  let fixture: ComponentFixture<VehicleListComponent>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRoute: jasmine.SpyObj<ActivatedRoute>;

  const createMockVehicle = (id: string): VehicleModel => {
    return new VehicleModel({
      id,
      is_leased: 0,
      code: 'V123',
      capacity: 4,
      wheelchair_capacity: 0,
      type: 'sedan',
      make: 'Toyota',
      model: 'Camry',
      year: '2020',
      color: 'black',
      license: 'ABC123',
      status: EVehicleStatus.free,
      service_area: {
        id: 1,
        name: 'LA',
        address: '123 Main St',
        location: 'Downtown',
        status: true,
        zone: {}
      },
      location: 'Downtown',
      driver_status: 'available',
      miles_count: 1000,
      has_assigned_drivers: false,
      oil_level: 100,
      oil_due: 5000,
      mileage_due: 10000,
      tire_size: '205/55R16',
      vin: '1HGCM82633A123456',
      description: 'Test vehicle',
      trips_count: 0,
      vehicle_type: { id: 1, name: 'Sedan' },
      recent_trips_completed: [],
      documents: [],
      created: '2024-01-01',
      oil_change_every: '5000',
      odometer_reading: 1000,
      is_air_filter_clean: true,
      condition_checks: [],
      photos: []
    });
  };

  beforeEach(async () => {
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockRoute = jasmine.createSpyObj('ActivatedRoute', [], {
      queryParams: of({}),
      params: of({})
    });

    await TestBed.configureTestingModule({
      declarations: [VehicleListComponent],
      providers: [
        { provide: MatDialog, useValue: mockDialog },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockRoute }
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(VehicleListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should assign vehicle to driver', () => {
    const mockVehicle = createMockVehicle('123');
    const mockDialogRef = {
      afterClosed: () => of(true)
    } as unknown as MatDialogRef<AssignToDriverComponent>;

    mockDialog.open.and.returnValue(mockDialogRef);

    component.assignToDriver(mockVehicle);

    expect(mockDialog.open).toHaveBeenCalledWith(AssignToDriverComponent, {
      width: '750px',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { vehicleId: '123' }
    });
  });

  it('should open vehicle documents', () => {
    const mockVehicle = createMockVehicle('123');
    const mockDialogRef = {
      afterClosed: () => of(null)
    } as unknown as MatDialogRef<VehicleDocumentsComponent>;

    mockDialog.open.and.returnValue(mockDialogRef);

    component.openVehicleDocuments(mockVehicle);

    expect(mockDialog.open).toHaveBeenCalledWith(VehicleDocumentsComponent, {
      width: '75vw',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { vehicleId: '123' }
    });
  });
});
