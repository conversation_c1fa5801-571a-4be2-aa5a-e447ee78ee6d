import { ComponentFixture, TestBed } from '@angular/core/testing';
import { VehicleFiltersComponent } from './vehicle-filters.component';
import { configureTestModule } from '@testing/test-setup';
import { IVehicleFilters } from '@modules/vehicle/models';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';

describe('VehicleFiltersComponent', () => {
  let component: VehicleFiltersComponent;
  let fixture: ComponentFixture<VehicleFiltersComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await configureTestModule([], [])
      .compileComponents();
    
    fixture = TestBed.createComponent(VehicleFiltersComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty form', () => {
    expect(component.formGroup.get('from_date')?.value).toBe('');
    expect(component.formGroup.get('to_date')?.value).toBe('');
    expect(component.formGroup.get('status')?.value).toBe('');
  });

  it('should update form when filters input changes', () => {
    const mockFilters: Partial<IVehicleFilters> = {
      from_date: '2024-03-20',
      to_date: '2024-03-21',
      status: 'active'
    };

    component.filters = mockFilters;

    expect(component.formGroup.get('from_date')?.value).toBe('2024-03-20T00:00:00.000Z');
    expect(component.formGroup.get('to_date')?.value).toBe('2024-03-21T00:00:00.000Z');
    expect(component.formGroup.get('status')?.value).toBe('active');
  });

  it('should reset form when filters input is undefined', () => {
    // First set some values
    component.filters = {
      from_date: '2024-03-20',
      to_date: '2024-03-21',
      status: 'active'
    };

    // Then reset
    component.filters = undefined;

    expect(component.formGroup.get('from_date')?.value).toBe('');
    expect(component.formGroup.get('to_date')?.value).toBe('');
    expect(component.formGroup.get('status')?.value).toBe('');
  });

  it('should emit filtered values when applyFilters is called', () => {
    const mockFilters: Partial<IVehicleFilters> = {
      from_date: '2024-03-20',
      to_date: '2024-03-21',
      status: 'active'
    };

    component.filters = mockFilters;
    
    const spy = spyOn(component.onFilterApply, 'emit');
    component.applyFilters();

    expect(spy).toHaveBeenCalledWith({
      from_date: '2024-03-20T00:00:00.000Z',
      to_date: '2024-03-21T00:00:00.000Z',
      status: 'active'
    });
  });

  it('should emit undefined when resetFilters is called', () => {
    const spy = spyOn(component.onFilterApply, 'emit');
    component.resetFilters();

    expect(spy).toHaveBeenCalledWith(undefined);
    expect(component.formGroup.get('from_date')?.value).toBe('');
    expect(component.formGroup.get('to_date')?.value).toBe('');
    expect(component.formGroup.get('status')?.value).toBe('');
  });

  it('should emit when closeFilters is called', () => {
    const spy = spyOn(component.onFilterClose, 'emit');
    component.closeFilters();
    expect(spy).toHaveBeenCalled();
  });
});
