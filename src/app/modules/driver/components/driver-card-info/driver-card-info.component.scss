.driver-card {
  display: flex;
  position: relative;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 0 4px 0 #D6DCE880;
  border-radius: 16px;
  padding: 1rem;
  gap: 2.5rem;

  .personal-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;

    .icon-more-button {
      position: absolute;
      right: 1.25rem;
      top: 1.5rem;
    }

    .driver-avatar {
      width: 94px;
      height: 94px;
      padding: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      background-color: #FFF0E0;
      text-transform: uppercase;
      border-radius: 16px;
      overflow: hidden;
      font-weight: 700;
      font-size: 2rem;
      color: #FF9500;

      > img {
        width: 94px;
        height: 94px;
        object-fit: cover;
        border-radius: inherit;
        object-position: center;
        user-select: none;
      }
    }

    .driver-name {
      font-size: 1.25rem;
      font-weight: 700;
    }

    .driver-contact {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      gap: 1rem;

      > a > .mat-icon {
        width: 48px;
        height: 48px;
        cursor: pointer;
      }
    }

    .join-date {
      text-align: center;

      > p {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #808FA4;
      }

      > span {
        font-weight: 600;
      }
    }

    .tag {
      padding: 8px;
      border-radius: 8px;
      background-color: #F9F9F9;
      display: inline-flex;
      align-items: center;
      font-weight: 500;
      color: #627186;
      gap: 0.5rem;

      .mat-mdc-icon-button {
        --mdc-icon-button-icon-size: 16px;
        --mdc-icon-button-state-layer-size: 20px;
        width: var(--mdc-icon-button-state-layer-size);
        height: var(--mdc-icon-button-state-layer-size);
        padding: 0;

        .mat-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .driver-status {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    background: #F9F9F9;
    border-radius: 12px;
    padding: 0.75rem;
    gap: 0.5rem;

    .status-icon {
      width: 10px;
      height: 10px;
      border-radius: 50%;

      &.active {
        background-color: #00A1FF;
      }

      &.inactive {
        background-color: #FF3B30;
      }
    }
  }

  .contact-info {
    width: 100%;

    .contact {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #F9F9F9;
      border: 1px solid #DFE9F5;
      border-radius: 8px;
      padding: 1rem;

      &:not(:last-child) {
        margin-bottom: 1rem;
      }

      .mat-icon {
        flex-shrink: 0;
      }

      span {
        display: -moz-box;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}

::ng-deep {
  .actions-menu {
    border: 1px solid #DFE9F5;
    border-radius: 12px !important;
    box-shadow: 0 4px 28px 0 #AEC0D740 !important;

    .mat-mdc-menu-item {
      --mat-menu-item-label-text-weight: 500;
      --mat-menu-item-label-text-size: 15px;
      min-height: 40px;

      .mat-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
      }
    }
  }

  .driver-status-select {
    .mdc-text-field--filled {
      background-color: #F9F9F9 !important;
    }

    .mat-mdc-select:not([aria-expanded='true']) {
      .mat-mdc-select-value {
        text-align: center;
      }
    }

    mat-select-trigger {
      &:before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-inline-end: 8px;
        display: inline-block;
      }

      &[aria-label='ACTIVE']:before {
        background-color: #00A1FF;
      }

      &[aria-label='PENDING']:before {
        background-color: #FF9500;
      }
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }

  .mat-mdc-select-panel {
    border-radius: 12px !important;
    box-shadow: 0 4px 28px 0 #AEC0D740 !important;
    border: 1px solid #DFE9F5;

    .mat-mdc-option {
      &:before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-inline-end: 8px;
        background-color: red;
      }

      &[aria-label='ACTIVE']:before {
        background-color: #00A1FF;
      }

      &[aria-label='PENDING']:before {
        background-color: #FF9500;
      }
    }
  }
}