<div class="driver-card">
    <div class="personal-info">
        <mat-icon class="icon-more-button" [matMenuTriggerFor]="menu"
                  *appHasPermission="'admin/drivers'; method: EPermissionMethod.update">
            more_vert
        </mat-icon>
        <div class="driver-avatar">
            @if (driver.image) {
                <img [src]="driver.image" alt="driver avatar image">
            } @else {
                {{ getDriverAvatar() }}
            }
        </div>
        <h3 class="driver-name">{{ driver.name }}</h3>
        <div class="driver-contact">
            <a *ngIf="driver.phone_number" routerLink="/chat" [queryParams]="{driverId: driver.id}"
               aria-label="Chat with the driver">
                <mat-icon svgIcon="message-card-icon" matRipple/>
            </a>
            <a *ngIf="driver.email" href="mailto:{{driver.email}}" aria-label="Send E-mail to the driver">
                <mat-icon svgIcon="mail-card-icon" matRipple/>
            </a>
            <a *ngIf="driver.phone_number" href="tel:{{driver.phone_number}}"
               aria-label="Call the driver directly">
                <mat-icon svgIcon="contact-card-icon" matRipple/>
            </a>
        </div>
        <div class="join-date">
            <p>Added since</p>
            <span>{{ driver.created | date: 'dd MMM yyyy' }}</span>
        </div>
        <div class="tag text-secondary">
            #{{ driver.employeeId || 'EmployeeId' }}
            <button mat-icon-button *appHasPermission="'admin/drivers'; method: EPermissionMethod.update"
                    (click)="changeEmployeeId()" matTooltip="Edit Employee Id" aria-label="Edit Driver Employee Id">
                <mat-icon class="icon-small" svgIcon="pencil-edit"/>
            </button>
        </div>
        <div class="tag">
            {{ driver.driver.driver_type }}
            <button mat-icon-button *appHasPermission="'admin/drivers'; method: EPermissionMethod.update"
                    (click)="changeDriverType()" aria-label="Edit Driver type">
                <mat-icon class="icon-small" svgIcon="pencil-edit"/>
            </button>
        </div>
        <div class="flex gap-3">
            <div class="tag">
                {{ driver.driver.gender || 'Unknown' }}
            </div>
            <div class="tag">
                {{ driver.driver.vehicle_type }}
            </div>
        </div>
    </div>
    @if (driver.status === EDriverStatus.active || driver.status === EDriverStatus.inactive) {
        <div class="driver-status">
            <span class="status-icon {{driver.status.toLowerCase()}}"></span>
            {{ driver.status === EDriverStatus.active ? 'Active' : 'Deactivated' }}
        </div>
    } @else {
        <mat-form-field class="driver-status-select w-full" appearance="fill">
            <mat-select (selectionChange)="activateDriver()" [(ngModel)]="driver.status"
                        placeholder="Select status for this vehicle">
                <mat-select-trigger [attr.aria-label]="driver.status">
                    {{ driver.status === EDriverStatus.pending ? 'Approval needed' : 'Active' }}
                </mat-select-trigger>
                <mat-option [attr.aria-label]="EDriverStatus.pending" [value]="EDriverStatus.pending">
                    Approval needed
                </mat-option>
                <mat-option [attr.aria-label]="EDriverStatus.active" [value]="EDriverStatus.active">
                    Accept
                </mat-option>
            </mat-select>
        </mat-form-field>
    }
    <div class="contact-info">
        <div class="contact" *ngIf="driver.phone_number">
            <p class="inline-flex align-items-center mb-0 gap-2">
                <mat-icon svgIcon="call_default"/>
                <span>{{ driver.phone_number }}</span>
            </p>
            <mat-icon [cdkCopyToClipboard]="driver.phone_number" class="cursor-pointer" matRipple svgIcon="copy"/>
        </div>
        <div class="contact" *ngIf="driver.email">
            <p class="inline-flex align-items-center mb-0 gap-2">
                <mat-icon svgIcon="email_icon"/>
                <span>{{ driver.email }}</span>
            </p>
            <mat-icon [cdkCopyToClipboard]="driver.email" class="cursor-pointer" matRipple svgIcon="copy"/>
        </div>
        <div class="contact">
            <p class="inline-flex align-items-center mb-0 gap-2">
                <mat-icon svgIcon="location_icon"/>
                <span>{{ driver.driver.address }}</span>
            </p>
            <mat-icon [cdkCopyToClipboard]="driver.driver.address" class="cursor-pointer"
                      matRipple svgIcon="copy"/>
        </div>
    </div>
</div>

<mat-menu class="actions-menu" #menu="matMenu">
    @if (driver.status === EDriverStatus.inactive) {
        <button (click)="reactivateDriver()" mat-menu-item>
            <mat-icon svgIcon="undo-box"/>
            <span>Reactivate</span>
        </button>
    } @else {
        <!--        <button mat-menu-item>-->
                <!--            <mat-icon svgIcon="invoicing:download_pdf"/>-->
                <!--            <span>Export all driver data</span>-->
                <!--        </button>-->
                <!--        <mat-divider/>-->
        <button mat-menu-item (click)="updateDriverInfo()">
            <mat-icon svgIcon="edit"/>
            <span>Update Driver's Info</span>
        </button>
        <button mat-menu-item (click)="resetDriverPassword()">
            <mat-icon svgIcon="key_icon"/>
            <span>Reset Driver's Password</span>
        </button>
        <mat-divider/>
        <button (click)="deactivateDriver()" mat-menu-item>
            <mat-icon svgIcon="block"/>
            <span style="color: #FF3B30">Deactivate</span>
        </button>
    }
</mat-menu>
