import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DataService, UtilsService } from '@core/services';
import { DriverModel, EDriverStatus } from '@modules/driver/models';
import { ResetPasswordComponent } from '@shared/components/reset-password/reset-password.component';
import { UpdateDriverTypeComponent } from '../update-driver/update-driver-type/update-driver-type.component';
import {
  UpdateDriverEmployeeIdComponent
} from '../update-driver/update-driver-employee-id/update-driver-employee-id.component';
import {
  UpdateDriverInfoComponent
} from '@modules/driver/components/update-driver/update-driver-info/update-driver-info.component';
import { EPermissionMethod } from '@modules/settings/models';

@Component({
  selector: 'app-driver-card-info',
  templateUrl: './driver-card-info.component.html',
  styleUrl: './driver-card-info.component.scss'
})
export class DriverCardInfoComponent {
  @Input({ required: true }) driver!: DriverModel;
  protected readonly EDriverStatus = EDriverStatus;
  protected readonly EPermissionMethod = EPermissionMethod;

  constructor(private dataService: DataService, private dialog: MatDialog, private utils: UtilsService) {
  }

  getDriverAvatar() {
    return this.driver.first_name[0].trim() + this.driver.last_name[0].trim();
  }

  async activateDriver() {
    this.utils.openConfirmDialog({
      title: 'Accept the driver',
      body: `Are you sure you want to accept ${this.driver.first_name} ${this.driver.last_name} as an envoy driver?\n`
        + 'Once accepted, the driver will be added to the system database and will be able to start running and completing trips.',
      question: 'Are you sure you want to accept this driver?',
      primary_button: 'Accept the driver',
      icon: 'user-tick-blue',
    }, { panelClass: 'envoy-dialog' }).afterClosed().subscribe(result => {
      if (result === 'yes') {
        this.dataService.post(`admin/drivers/${this.driver.id}/change_status`, { status: EDriverStatus.active })
          .then(res => {
            if (res.status_code === 200) {
              this.utils.openSnackBar('Driver status updated successfully.', 5000, 'success');
            } else {
              this.driver.status = EDriverStatus.pending;
              this.utils.openSnackBar(res.data.message, 5000, 'error');
            }
          });
      }
    });
  }

  async reactivateDriver() {
    this.utils.openConfirmDialog({
      title: 'Reactivate the driver',
      body: 'Once reactivated, the driver will be able to resume running and completing trips.',
      question: 'Are you sure you want to reactivate this driver?',
      primary_button: 'Reactivate the driver',
      icon: 'user-tick-blue',
    }, { panelClass: 'envoy-dialog' }).afterClosed().subscribe(result => {
      if (result === 'yes') {
        this.dataService.post(`admin/drivers/${this.driver.id}/change_status`, { status: EDriverStatus.active })
          .then(res => {
            if (res.status_code === 200) {
              this.driver.status = EDriverStatus.active;
              this.utils.openSnackBar('Driver status updated successfully.', 5000, 'success');
            } else {
              this.utils.openSnackBar(res.data.message, 5000, 'error');
            }
          });
      }
    });
  }

  async deactivateDriver() {
    this.utils.openConfirmDialog({
      title: 'Deactivate the driver',
      body: `Are you sure you want to deactivate <strong>${this.driver.first_name} ${this.driver.last_name}</strong>?\n`
        + 'Once deactivated, the driver will no longer be able to run or complete trips.\n All assigned and accepted trip by this driver will be returned to available.',
      question: 'Are you sure you want to deactivate this driver?',
      primary_button: 'Deactivate the driver',
      icon: 'block',
      class: 'warning'
    }, { panelClass: 'envoy-dialog' }).afterClosed().subscribe(result => {
      if (result === 'yes') {
        this.dataService.post(`admin/drivers/${this.driver.id}/change_status`, { status: EDriverStatus.inactive })
          .then(res => {
            if (res.status_code === 200) {
              this.driver.status = EDriverStatus.inactive;
              this.utils.openSnackBar('Driver status updated successfully.', 5000, 'success');
            } else {
              this.utils.openSnackBar(res.data.message, 5000, 'error');
            }
          });
      }
    });
  }

  resetDriverPassword() {
    this.dialog.open(ResetPasswordComponent, {
      panelClass: 'envoy-dialog',
      disableClose: true,
      maxWidth: '750px',
      autoFocus: false,
      data: { userId: this.driver.id, endpoint: 'admin/drivers' }
    });
  }

  changeEmployeeId() {
    this.dialog.open(UpdateDriverEmployeeIdComponent, {
      width: '750px',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { id: this.driver.id, employeeId: this.driver.employeeId }
    }).afterClosed().subscribe(result => {
      if (result) {
        this.driver.employeeId = result;
      }
    });
  }

  changeDriverType() {
    this.dialog.open(UpdateDriverTypeComponent, {
      width: '750px',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { id: this.driver.id, driverType: this.driver.driver.driver_type }
    }).afterClosed().subscribe(result => {
      if (result) {
        this.driver.driver.driver_type = result;
      }
    });
  }

  updateDriverInfo() {
    this.dialog.open(UpdateDriverInfoComponent, {
      width: '750px',
      autoFocus: false,
      panelClass: 'envoy-dialog',
      data: { driver: this.driver }
    }).afterClosed().subscribe(data => {
      if (data) {
        this.driver = data;
      }
    });
  }
}
