import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';

import { DriverCardInfoComponent } from './driver-card-info.component';
import { DataService, UtilsService } from '@core/services';
import { MockUtilsService } from '../../../../testing/test-utils';

describe('DriverCardInfoComponent', () => {
  let component: DriverCardInfoComponent;
  let fixture: ComponentFixture<DriverCardInfoComponent>;
  let mockDataService: jasmine.SpyObj<DataService>;
  let mockUtilsService: MockUtilsService;

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['get', 'post', 'put']);
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      declarations: [DriverCardInfoComponent],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatDividerModule,
        MatTabsModule
      ],
      providers: [
        { provide: DataService, useValue: dataServiceSpy },
        { provide: UtilsService, useValue: mockUtilsService },
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({ id: 1 }),
            queryParams: of({}),
            data: of({})
          }
        }
      ]
    })
    .compileComponents();

    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockDataService.get.and.returnValue(Promise.resolve({ status_code: 200, data: {} }));

    fixture = TestBed.createComponent(DriverCardInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
