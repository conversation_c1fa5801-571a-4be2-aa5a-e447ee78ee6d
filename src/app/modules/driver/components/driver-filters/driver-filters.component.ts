import { Component, EventEmitter, Input, Output } from '@angular/core';
import { VehicleTypeModel } from '@modules/profiles/models';
import { IDriverFilters } from '../../models';
import { FormBuilder } from '@angular/forms';
import { DataService } from '@core/services';
import { defer, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {TConfigRecord} from "@shared/models";

@Component({
  selector: 'app-driver-filters',
  templateUrl: './driver-filters.component.html',
  styleUrl: './driver-filters.component.scss'
})
export class DriverFiltersComponent {
  formGroup = this.initFormGroup();
  vehicleTypes = this.getVehicleTypes();
  @Output() onFilterApply = new EventEmitter<IDriverFilters | undefined>();
  @Output() onFilterClose = new EventEmitter();

  constructor(private fb: FormBuilder, private dataService: DataService) {
  }

  @Input() set filters(_filters: Partial<IDriverFilters> | undefined) {
    if (!_filters) {
      this.formGroup.reset();
    } else {
      const filtersWithISODate = {
        ..._filters,
        from_date: _filters.from_date ? new Date(_filters.from_date).toISOString() : '',
        geo_code: _filters.geo_code ? _filters.geo_code  : '',
        to_date: _filters.to_date ? new Date(_filters.to_date).toISOString() : ''
      };
      this.formGroup.patchValue(filtersWithISODate);
    }
  }

  applyFilters() {
    if(this.formGroup.valid) {
      const _filters: any = {};
      Object.entries(this.formGroup.getRawValue()).map(([key, value]) => {
        value && (_filters[key] = value);
      });
      this.onFilterApply.emit(Object.keys(_filters).length ? _filters : undefined);
    }
  }

  resetFilters() {
    this.formGroup.reset();
    this.onFilterApply.emit(undefined);
  }

  closeFilters() {
    this.onFilterClose.emit();
  }

  selectVehicleType(id: number) {
    this.formGroup.patchValue({ vehicle: id });
  }

  private getVehicleTypes(): Observable<VehicleTypeModel[]> {
    return defer(() => this.dataService.get(`lookup/vehicle_types`))
      .pipe(map(res => {
        if (res && res.status_code === 200 && Array.isArray(res.data)) {
          return res.data;
        }
        return [];
      }));
  }

  private initFormGroup() {
    return this.fb.nonNullable.group({
      search_location: [''],
      from_date: [''],
      to_date: [''],
      vehicle: [null as unknown as number],
      status:'',
      geo_code:'',
    });
  }

  handleAreaChange(area: any) {
    if (area) {
      this.formGroup.patchValue({geo_code:area.id});
    }
  }

  handleAreaWrite(){
    if (this.formGroup.get("geo_code")?.getRawValue() != null) {
      this.formGroup.get("geo_code")?.reset();
    }
  }

  displayArea(area: TConfigRecord) {
    return area ? area.key : '';
  }
}
