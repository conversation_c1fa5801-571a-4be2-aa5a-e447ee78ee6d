<div class="sidenav__header">
    <h3 class="close-btn" (click)="closeFilters()">
        <mat-icon>keyboard_arrow_left</mat-icon>
        Close
    </h3>
    <h3 class="title pr-4">Table Filters</h3>
</div>
<mat-divider/>
<form [formGroup]="formGroup" class="sidenav__content">
    <div class="section">
        <h4 class="title-icon">
            <mat-icon svgIcon="calender-icon"></mat-icon>
            Registration Date
        </h4>
        <div class="grid mx-0">
            <div class="filter-box col-12 md:col-6">
                <mat-form-field appearance="outline" class="sm-form-field w-full">
                    <mat-label>From Date</mat-label>
                    <input type="text" [matDatepicker]="picker" (click)="picker.open()" matInput
                           placeholder="From Date" formControlName="from_date" name="from_date"/>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="filter-box col-12 md:col-6">
                <mat-form-field appearance="outline" class="sm-form-field w-full">
                    <mat-label>To Date</mat-label>
                    <input type="text" [min]="formGroup.get('from_date')?.value" [matDatepicker]="toPicker"
                           (click)="toPicker.open()" matInput
                           placeholder="To Date" formControlName="to_date" name="to_date"/>
                    <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
                    <mat-datepicker #toPicker></mat-datepicker>
                    <mat-error *ngIf="formGroup.get('to_date')?.errors?.['matDatepickerMin']">
                      The Date should be greater than from date
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
    </div>

    <mat-divider/>

    <div class="section">
        <h4 class="title-icon">
            <mat-icon svgIcon="car_types_icon"/>
            Car type
        </h4>
        <div class="px-2 flex flex-wrap gap-2">
            <ng-container *ngFor="let vehicle of vehicleTypes | async">
                <button mat-button class="filter-button" (click)="selectVehicleType(vehicle.id)">
                  <span class="flex-justify-between" style="color: #000">{{ vehicle.name }}
                      <mat-icon *ngIf="formGroup.get('vehicle')?.value != vehicle.id">add</mat-icon>
                      <mat-icon *ngIf="formGroup.get('vehicle')?.value == vehicle.id">remove</mat-icon>
                  </span>
                </button>
            </ng-container>
        </div>
    </div>

    <mat-divider/>
    <div class="section">
      <h4 class="title-icon">
        <mat-icon svgIcon="car_types_icon"/>
        Geo Codes to filter
      </h4>
      <div class="px-2">
        <mat-dialog-content>
          <div class="w-full col-12">
            <envoy-auto-complete-input formControlName="geo_code"
                                       [optionLabels]="['key']" [optionValue]="'id'"
                                       [query]="['lookup', 'area_filer_code']" [displayWith]="displayArea"
                                       (change)="handleAreaWrite()" (selectionChange)="handleAreaChange($event)"
                                       placeholder="Select the Geo Code to add to driver."/>
          </div>

        </mat-dialog-content>
      </div>
    </div>
    <mat-divider/>
    <div class="section">
        <div class="grid px-2">
            <div class="col-6">
                <button class="w-full" type="reset" mat-raised-button (click)="resetFilters()">Reset Filter</button>
            </div>
            <div class="col-6">
                <button class="w-full" type="button" mat-raised-button (click)="applyFilters()" color="success">
                    Apply Filter
                </button>
            </div>
        </div>
    </div>
</form>
