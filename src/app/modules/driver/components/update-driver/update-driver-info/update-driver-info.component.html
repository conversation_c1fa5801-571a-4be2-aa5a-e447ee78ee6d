<div class="corporate-dialog__title" mat-dialog-title>
    <h2 class="title-icon">
        <mat-icon svgIcon="user-edit"/>
        Edit driver's profile
    </h2>
    <button mat-button mat-dialog-close>
        <mat-icon svgIcon="close-icon"/>
    </button>
</div>
<mat-dialog-content class="p-3">
    <form [formGroup]="formGroup" class="grid mx-0">
        <div class="col-6">
            <mat-label class="inline-block mb-1">First name</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="first_name" placeholder="Enter driver's first name">
                <mat-error>The Field is Required.</mat-error>
            </mat-form-field>
        </div>
        <div class="col-6">
            <mat-label class="inline-block mb-1">Last name</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="last_name" placeholder="Enter driver's last name">
                <mat-error>The Field is Required.</mat-error>
            </mat-form-field>
        </div>
        <div class="col-6">
            <mat-label class="inline-block mb-1">Email address</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="email" placeholder="Enter driver's email address">
                <mat-error *ngIf="formGroup.get('email')?.getError('required')">
                    The Field is Required.
                </mat-error>
                <mat-error *ngIf="formGroup.get('email')?.getError('email')">
                    Not A Valid Email.
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-6">
            <mat-label class="inline-block mb-1">Phone Number</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <envoy-tel-input formControlName="phone_number" placeholder="Ex: (*************"/>
                <mat-error *ngIf="formGroup.get('phone_number')?.getError('required')">
                    The Field is Required.
                </mat-error>
                <mat-error *ngIf="formGroup.get('phone_number')?.getError('invalid')">
                    Invalid phone number.
                </mat-error>
            </mat-form-field>
        </div>
    </form>
</mat-dialog-content>
<mat-dialog-actions>
    <div class="w-full px-2 flex gap-3">
        <button mat-stroked-button mat-dialog-close class="col-3">Cancel</button>
        <button (click)="updateDriverInfo()" mat-flat-button color="primary" class="col">
            Update driver info
        </button>
    </div>
</mat-dialog-actions>
