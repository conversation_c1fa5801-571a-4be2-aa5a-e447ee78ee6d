import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { emailValidator, noWhiteSpaceValidator, phoneValidator } from '@shared/validators';
import { DataService, UtilsService } from '@core/services';
import { DriverModel } from '@modules/driver/models';

@Component({
  selector: 'app-update-driver-info',
  templateUrl: './update-driver-info.component.html',
  styleUrl: './update-driver-info.component.scss'
})
export class UpdateDriverInfoComponent implements OnInit {
  formGroup = this.fb.nonNullable.group({
    first_name: ['', [Validators.required, noWhiteSpaceValidator]],
    last_name: ['', [Validators.required, noWhiteSpaceValidator]],
    phone_number: ['', [Validators.required, phoneValidator]],
    email: ['', [Validators.required, emailValidator]],
  });

  constructor(private fb: FormBuilder, private dataService: DataService, private utils: UtilsService,
              @Inject(MAT_DIALOG_DATA) private data: { driver: DriverModel },
              private dialogRef: MatDialogRef<UpdateDriverInfoComponent>) {
  }

  ngOnInit() {
    this.formGroup.patchValue({
      first_name: this.data.driver.first_name,
      last_name: this.data.driver.last_name,
      phone_number: this.data.driver.phone_number,
      email: this.data.driver.email
    });
  }

  updateDriverInfo() {
    if (!this.formGroup.valid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    this.utils.openConfirmDialog({
        title: `${this.data.driver.name}`,
        question: 'Are you sure you want to confirm updating this driver profile info?',
        body: 'Your are about to update this driver info to the system’s Database.',
        primary_button: 'Update Driver',
        icon: 'user-tick-blue',
      },
      { panelClass: 'envoy-dialog' }
    ).afterClosed().subscribe(result => {
      if (result === 'yes') {
        this.dataService.put(`admin/drivers/${this.data.driver.id}`, this.formGroup.getRawValue())
          .then(data => {
            if (data.status_code === 200) {
              this.utils.openSnackBar('Profile Info for this driver updated successfully.', 5000, 'success');
              this.dialogRef.close(new DriverModel(data.data));
            } else {
              this.utils.openSnackBar(data.data.message, 5000, 'error');
            }
          });
      }
    });
  }
}
