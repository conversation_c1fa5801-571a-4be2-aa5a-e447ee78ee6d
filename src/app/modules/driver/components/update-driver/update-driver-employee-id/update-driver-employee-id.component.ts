import { Component, Inject } from '@angular/core';
import { DataService, UtilsService } from '@core/services';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-update-driver-employee-id',
  templateUrl: './update-driver-employee-id.component.html',
  styleUrl: './update-driver-employee-id.component.scss'
})
export class UpdateDriverEmployeeIdComponent {
  employeeId: string | undefined;

  constructor(private dataService: DataService, private utils: UtilsService,
              @Inject(MAT_DIALOG_DATA) private data: { id: number, employeeId: string },
              private dialogRef: MatDialogRef<UpdateDriverEmployeeIdComponent>) {

    this.employeeId = data.employeeId;
  }

  updateDriver() {
    if (!this.employeeId) return;
    this.utils.openConfirmDialog({
      title: 'Update Driver Employee ID',
      body: `Your are about to change this driver employee ID to <strong>${this.employeeId}</strong>.`,
      question: 'Are you sure you want to update this driver Employee ID?',
      primary_button: 'Update Driver Employee Id',
      icon: 'user-tick-blue',
    }).afterClosed().subscribe(status => {
      if (status === 'yes') {
        this.dataService.post(`admin/drivers/${this.data.id}/assign_employee_id`, {
          employee_id: this.employeeId
        }).then(data => {
          if (data.status_code === 200) {
            this.utils.openSnackBar('Driver Employee ID updated successfully.', 5000, 'success');
            this.dialogRef.close(this.employeeId);
          } else {
            this.utils.openSnackBar(data.data.message, 5000, 'error');
          }
        });
      }
    });
  }
}
