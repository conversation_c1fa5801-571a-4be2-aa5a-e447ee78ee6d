<div class="corporate-dialog__title" mat-dialog-title>
    <h2 class="title-icon">
        <mat-icon svgIcon="user-edit"/>
        Edit driver Employee ID
    </h2>
    <button mat-button mat-dialog-close>
        <mat-icon svgIcon="close-icon"/>
    </button>
</div>

<mat-dialog-content class="p-3 h-10rem">
    <mat-form-field class="w-full" appearance="outline">
        <mat-label>Employee ID</mat-label>
        <input matInput [(ngModel)]="employeeId" placeholder="Enter employee id for this driver">
    </mat-form-field>
</mat-dialog-content>

<mat-dialog-actions>
    <div class="w-full px-2 flex gap-3">
        <button mat-stroked-button mat-dialog-close class="col-3">Cancel</button>
        <button (click)="updateDriver()" mat-flat-button color="primary" class="col">
            Update Employee Id
        </button>
    </div>
</mat-dialog-actions>