import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { of } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';

import { UpdateDriverExtraDataComponent } from './update-driver-extra-data.component';
import { DataService, UtilsService } from '@core/services';
import { MockUtilsService } from '../../../../../testing/test-utils';

describe('UpdateDriverExtraDataComponent', () => {
  let component: UpdateDriverExtraDataComponent;
  let fixture: ComponentFixture<UpdateDriverExtraDataComponent>;
  let mockDataService: jasmine.SpyObj<DataService>;
  let mockUtilsService: MockUtilsService;

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['get', 'post', 'put']);
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      declarations: [UpdateDriverExtraDataComponent],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule
      ],
      providers: [
        { provide: DataService, useValue: dataServiceSpy },
        { provide: UtilsService, useValue: mockUtilsService },
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({ driverId: 1 }),
            queryParams: of({}),
            snapshot: { params: { driverId: 1 }, queryParams: {} },
            data: of({})
          }
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { driverId: 1, driver: { id: 1 } }
        },
        {
          provide: MatDialogRef,
          useValue: {
            close: jasmine.createSpy('close')
          }
        }
      ]
    })
    .compileComponents();

    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockDataService.get.and.returnValue(Promise.resolve({ status_code: 200, data: {} }));

    fixture = TestBed.createComponent(UpdateDriverExtraDataComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
