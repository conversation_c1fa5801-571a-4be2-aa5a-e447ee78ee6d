import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataService, UtilsService } from '@core/services';
import { DriverModel } from '@modules/driver/models';
import { FormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-update-driver-extra-data',
  templateUrl: './update-driver-extra-data.component.html',
  styleUrl: './update-driver-extra-data.component.scss'
})
export class UpdateDriverExtraDataComponent implements OnInit {
  formGroup = this.initFormGroup();

  constructor(private dataService: DataService, private utils: UtilsService, private fb: FormBuilder,
              @Inject(MAT_DIALOG_DATA) private data: { driver: DriverModel },
              private dialogRef: MatDialogRef<UpdateDriverExtraDataComponent>) {
  }

  ngOnInit() {
    if (this.data.driver.driver.extra_data) {
      this.formGroup.patchValue(this.data.driver.driver.extra_data);
    }
  }

  updateDriverData() {
    if (!this.formGroup.valid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    this.utils.openConfirmDialog({
        title: `${this.data.driver.name}`,
        question: 'Are you sure you want to confirm updating this driver extra data?',
        body: 'Your are about to update this driver extra data to the system’s Database.',
        primary_button: 'Update Driver extra data',
        icon: 'user-tick-blue',
      },
      { panelClass: 'envoy-dialog' }
    ).afterClosed().subscribe(result => {
      if (result === 'yes') {
        const {
          driver_license_date_of_expiration,
          vehicle_registration_expiration_date,
          ..._data
        } = this.formGroup.getRawValue();
        this.dataService.put(`admin/drivers/${this.data.driver.id}`, {
          driver: {
            extra_data: {
              ..._data,
              driver_license_date_of_expiration: this.utils.clearDate(driver_license_date_of_expiration),
              vehicle_registration_expiration_date: this.utils.clearDate(vehicle_registration_expiration_date)
            }
          }
        }).then(data => {
          if (data.status_code === 200) {
            this.utils.openSnackBar('Driver extra data updated successfully.', 5000, 'success');
            this.dialogRef.close(data.data.driver.extra_data);
          } else {
            this.utils.openSnackBar(data.data.message, 5000, 'error');
          }
        });
      }
    });
  }

  private initFormGroup() {
    return this.fb.nonNullable.group({
      driver_license_state: ['', [Validators.maxLength(50)]],
      driver_license_document_number: ['', [Validators.maxLength(50)]],
      driver_license_date_of_expiration: [null as unknown as string],
      vehicle_registration_expiration_date: [null as unknown as string],
      vehicle_make_model_year_color: ['', [Validators.maxLength(255)]],
      license_plate: ['', [Validators.maxLength(50)]],
      license_state: ['', [Validators.maxLength(20)]],
      vin: ['', [Validators.maxLength(17), Validators.pattern('^[A-Z0-9]+$')]]
    });
  }
}
