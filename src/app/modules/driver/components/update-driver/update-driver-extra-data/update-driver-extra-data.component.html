<div class="corporate-dialog__title" mat-dialog-title>
    <h2 class="title-icon">
        <mat-icon svgIcon="presentation_icon"/>
        Edit Driver Extra Data
    </h2>
    <button mat-button mat-dialog-close>
        <mat-icon svgIcon="close-icon"/>
    </button>
</div>

<mat-dialog-content class="p-3">
    <form [formGroup]="formGroup" class="grid mx-0">
        <div class="col-6">
            <mat-label class="inline-block mb-1">Driver license state</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="driver_license_state" placeholder="Enter driver license state">
                <mat-error *ngIf="formGroup.get('driver_license_state')?.getError('maxlength')">
                  Must be 50 characters max.
                </mat-error>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">Driver license document number</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="driver_license_document_number"
                       placeholder="Enter driver license document number">
                <mat-error *ngIf="formGroup.get('driver_license_document_number')?.getError('maxlength')">
                  Must be 50 characters max.
                </mat-error>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">Driver license date of expiration</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input type="text" [matDatepicker]="picker" (click)="picker.open()" matInput
                       placeholder="Enter driver license date of expiration"
                       formControlName="driver_license_date_of_expiration"
                       name="driver_license_date_of_expiration"/>
                <mat-datepicker-toggle matSuffix [for]="picker"/>
                <mat-datepicker #picker/>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">Vehicle registration expiration date</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input type="text" [matDatepicker]="picker2" (click)="picker2.open()" matInput
                       placeholder="Enter vehicle registration expiration date"
                       formControlName="vehicle_registration_expiration_date"
                       name="vehicle_registration_expiration_date"/>
                <mat-datepicker-toggle matSuffix [for]="picker2"/>
                <mat-datepicker #picker2/>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">Vehicle make model color</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="vehicle_make_model_year_color" placeholder="Enter vehicle make model color">
                <mat-error *ngIf="formGroup.get('vehicle_make_model_year_color')?.getError('maxlength')">
                  Must be 255 characters max.
                </mat-error>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">License plate</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="license_plate" placeholder="Enter license plate">
                <mat-error *ngIf="formGroup.get('license_plate')?.getError('maxlength')">
                  License plate must be 50 characters max.
                </mat-error>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">License state</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="license_state" placeholder="Enter license state">
                <mat-error *ngIf="formGroup.get('license_state')?.getError('maxlength')">
                  License State must be 20 characters max.
                </mat-error>
            </mat-form-field>
        </div>

        <div class="col-6">
            <mat-label class="inline-block mb-1">Vin</mat-label>
            <mat-form-field appearance="outline" class="w-full">
                <input matInput formControlName="vin" placeholder="Enter vin">
                <mat-error *ngIf="formGroup.get('vin')?.getError('maxlength')">
                    Vin must be 17 characters max.
                </mat-error>
                <mat-error *ngIf="formGroup.get('vin')?.getError('pattern')">
                    Only capital letters and numbers are allowed.
                </mat-error>
            </mat-form-field>
        </div>
    </form>
</mat-dialog-content>

<mat-dialog-actions>
    <div class="w-full px-2 flex gap-3">
        <button mat-stroked-button mat-dialog-close class="col-3">Cancel</button>
        <button (click)="updateDriverData()" mat-flat-button color="primary" class="col">
            Update driver data
        </button>
    </div>
</mat-dialog-actions>
