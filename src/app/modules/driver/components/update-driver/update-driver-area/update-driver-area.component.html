<div class="corporate-dialog__title" mat-dialog-title>
    <h2 class="title-icon">
        <mat-icon svgIcon="location_icon"/>
        Edit Geo Code
    </h2>
    <button mat-button mat-dialog-close>
        <mat-icon svgIcon="close-icon"/>
    </button>
</div>

<mat-dialog-content class="p-3 h-10rem">
    <div class="w-full">
        <envoy-auto-complete-input label="Geo Codes to filter" [formControl]="areaFilterCtrl"
                                   [optionLabels]="['key']" [optionValue]="'id'"
                                   [query]="['lookup', 'area_filer_code']" [displayWith]="displayArea"
                                   (change)="handleAreaWrite()" (selectionChange)="handleAreaChange($event)"
                                   placeholder="Select the Geo Code to add to driver."/>
    </div>

</mat-dialog-content>

<mat-dialog-actions>
    <div class="w-full px-2 flex gap-3">
        <button mat-stroked-button mat-dialog-close class="col-3">Cancel</button>
        <button (click)="updateDriverArea()" mat-flat-button color="primary" class="col">
            Update Geo Code
        </button>
    </div>
</mat-dialog-actions>
