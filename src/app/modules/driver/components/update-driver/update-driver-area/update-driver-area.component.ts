import { FormControl } from '@angular/forms';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataService, UtilsService } from '@core/services';
import { TConfigRecord } from '@shared/models';

@Component({
  selector: 'app-update-driver-area',
  templateUrl: './update-driver-area.component.html',
  styleUrl: './update-driver-area.component.scss'
})
export class UpdateDriverAreaComponent {
  areaFilterCtrl = new FormControl('');

  constructor(private dataService: DataService, private utils: UtilsService,
              @Inject(MAT_DIALOG_DATA) private data: { id: number, area: string },
              private dialogRef: MatDialogRef<UpdateDriverAreaComponent>) {
    this.areaFilterCtrl.patchValue(data.area);
  }

  updateDriverArea() {
    if(this.areaFilterCtrl.value) {
      this.utils.openConfirmDialog({
        title: 'Update Driver Geo Code',
        body: `Your are about to change this driver Geo Code to <strong>${this.areaFilterCtrl.value}</strong>.`,
        question: 'Are you sure you want to update this driver Geo Code?',
        primary_button: 'Update Geo Code',
        icon: 'user-tick-blue',
      }).afterClosed().subscribe(status => {
        if (status === 'yes') {
          this.dataService.put(`admin/drivers/${this.data.id}`, {
            area_filter: this.areaFilterCtrl.value
          }).then(data => {
            if (data.status_code === 200) {
              this.utils.openSnackBar('Driver Geo Code updated successfully.', 5000, 'success');
              this.dialogRef.close(data.data.geo_code);
            } else {
              this.utils.openSnackBar(data.data.message, 5000, 'error');
            }
          });
        }
      });
    }
    else{
      this.areaFilterCtrl.reset();
    }
  }

  handleAreaChange(area: any) {
    if (area) {
      this.areaFilterCtrl.patchValue(area.id);
    }
  }

  handleAreaWrite(){
    if (this.areaFilterCtrl.getRawValue() != null) {
      this.areaFilterCtrl.reset();
    }
  }

  displayArea(area: TConfigRecord) {
    return area ? area.key : '';
  }
}
