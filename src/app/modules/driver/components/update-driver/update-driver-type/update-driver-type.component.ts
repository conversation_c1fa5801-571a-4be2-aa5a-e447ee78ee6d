import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataService, UtilsService } from '@core/services';
import { TDriverType } from '@modules/driver/models';

@Component({
  selector: 'app-update-driver-type',
  templateUrl: './update-driver-type.component.html',
  styleUrl: './update-driver-type.component.scss'
})
export class UpdateDriverTypeComponent {
  driverType!: TDriverType;

  constructor(private dataService: DataService, private utils: UtilsService,
              @Inject(MAT_DIALOG_DATA) private data: { id: number, driverType: TDriverType },
              private dialogRef: MatDialogRef<UpdateDriverTypeComponent>) {
    this.driverType = data.driverType;
  }

  updateDriver() {
    if (!this.driverType) return;
    this.utils.openConfirmDialog({
      title: 'Update Driver Type',
      body: `Your are about to change this driver contract type to <strong>${this.driverType}</strong>.`,
      question: 'Are you sure you want to update this driver contract type?',
      primary_button: 'Update Driver Type',
      icon: 'user-tick-blue',
    }).afterClosed().subscribe(status => {
      if (status === 'yes') {
        this.dataService.put(`admin/drivers/${this.data.id}`, {
          driver: { driver_type: this.driverType }
        }).then(data => {
          if (data.status_code === 200) {
            this.utils.openSnackBar('Driver type updated successfully.', 5000, 'success');
            this.dialogRef.close(data.data.driver.driver_type);
          } else {
            this.utils.openSnackBar(data.data.message, 5000, 'error');
          }
        });
      }
    });
  }
}
