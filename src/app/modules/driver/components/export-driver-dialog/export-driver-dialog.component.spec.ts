import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { ExportDriverDialogComponent } from './export-driver-dialog.component';
import { UtilsService } from '@core/services';
import { MockUtilsService } from '../../../../testing/test-utils';

describe('ExportDriverDialogComponent', () => {
  let component: ExportDriverDialogComponent;
  let fixture: ComponentFixture<ExportDriverDialogComponent>;
  let mockUtilsService: MockUtilsService;

  beforeEach(async () => {
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      imports: [
        ExportDriverDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: UtilsService, useValue: mockUtilsService },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            title: 'Test Export',
            endpoint: '/api/test',
            confirm_label: 'Export'
          }
        },
        {
          provide: MatDialogRef,
          useValue: {
            close: jasmine.createSpy('close')
          }
        }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ExportDriverDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
