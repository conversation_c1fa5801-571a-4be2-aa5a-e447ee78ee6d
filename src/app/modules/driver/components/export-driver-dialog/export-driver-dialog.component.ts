import { NgIf } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDatepickerModule, } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { UtilsService } from '@core/services';
import { ProgressSpinnerComponent } from "@shared/components/progress-spinner/progress-spinner.component";
import {MatOption} from "@angular/material/autocomplete";
import {MatSelect} from "@angular/material/select";
import {MatRadioButton, MatRadioGroup} from "@angular/material/radio";

export interface IDialogData {
  title: string;
  endpoint: string;
  confirm_label: string;
  fileName?: string;
  fromQuery?: string;
  toQuery?: string;
  progress?: boolean
}

@Component({
  selector: 'envoy-export-driver-dialog',
  templateUrl: './export-driver-dialog.component.html',
  styleUrl: './export-driver-dialog.component.scss',
  standalone: true,
  imports: [
    MatDialogModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatButton,
    MatInput,
    NgIf,
    MatOption,
    MatSelect,
    MatRadioButton,
    MatRadioGroup
  ]
})
export class ExportDriverDialogComponent {
  fileName = 'exported_file_';
  showSpinner = false;

  formGroup = this.fb.nonNullable.group({
    status: ['All', [Validators.required]],
    type: [null]
  });

  constructor(private fb: FormBuilder, @Inject(MAT_DIALOG_DATA) public data: IDialogData,
              private dialogRef: MatDialogRef<ExportDriverDialogComponent>, private dialog: MatDialog,
              private http: HttpClient, private utils: UtilsService) {
    this.fileName = data.fileName ?? this.fileName;
    this.showSpinner = data.progress ?? this.showSpinner;
  }

  exportDriver() {
    if (this.formGroup.valid) {
      let apiUrl = `${this.data.endpoint}?status=${this.formGroup.get('status')?.value}`;

      if (this.formGroup.get('type')?.value) {
        apiUrl += `&type=${this.formGroup.get('type')?.value}`
      }

      const spinnerRef = this.dialog.open(ProgressSpinnerComponent, {
        disableClose: true,
        data: {},
        panelClass: 'progress-spinner'
      });
      let response_type = 'text';
      let blob_type =  'text/csv';
      let extension_type = '.csv';
      if (this.formGroup.get('type')?.value){
        response_type = 'ArrayBuffer';
        blob_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        extension_type = '.xlsx';
      }
      // @ts-ignore
      this.http.get(apiUrl, { responseType:  response_type})
        .subscribe((data: any) => {
          // transform data to blob and create url
          const blob = new Blob([data], { type: blob_type });
          const url = window.URL.createObjectURL(blob);
          // Create a link element
          const link = document.createElement('a');

          // Set the link's href attribute to a URL representing the Blob
          link.href = url;

          // Specify the filename for the download

          link.download = this.fileName + new Date().toLocaleString(undefined, {
            dateStyle: 'full',
            timeStyle: 'short'
          }) + extension_type;

          link.click();

          window.URL.revokeObjectURL(url);
          spinnerRef.close();
          this.dialogRef.close('Downloaded Successfully');
        }, (err) => {
          spinnerRef.close();
          this.utils.openSnackBar(JSON.parse(err.error).data.message, 5000, 'error');
        });
    } else {
      this.formGroup.markAllAsTouched();
    }
  }
}
