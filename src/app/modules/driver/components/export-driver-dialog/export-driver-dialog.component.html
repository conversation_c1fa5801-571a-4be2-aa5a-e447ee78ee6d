<h1 mat-dialog-title>{{ data.title }}</h1>
<div mat-dialog-content class="py-4" [formGroup]="formGroup">
    <mat-form-field appearance="outline" class="w-full">
        <mat-label>Status</mat-label>
        <mat-select formControlName="status">
          <mat-option value="All">All</mat-option>
          <mat-option value="ACTIVE">Active</mat-option>
          <mat-option value="PENDING">Pending</mat-option>
          <mat-option value="INACTIVE">Inactive</mat-option>
        </mat-select>
        <mat-error *ngIf="formGroup.get('status')?.getError('required')">This field is required</mat-error>
    </mat-form-field>
  <div class="condition my-2">
    <mat-radio-group class="grid gap-3 px-2" formControlName="type" labelPosition="after" color="primary" aria-label="Export Driver type">
      <mat-radio-button [value]="null">Default CSV</mat-radio-button>
      <mat-radio-button value="required">Required Excel</mat-radio-button>
      <mat-radio-button value="all">All Excel</mat-radio-button>
      <mat-radio-button value="ac2">Access2Care Excel</mat-radio-button>
    </mat-radio-group>
  </div>
</div>
<div class="grid" mat-dialog-actions>
    <div class="col-6">
        <button class="w-full" mat-raised-button color="primary" (click)="exportDriver()">
            {{ data.confirm_label }}
        </button>
    </div>
    <div class="col-6">
        <button class="w-full button" mat-button color="primary" mat-dialog-close>
            Cancel
        </button>
    </div>
</div>
