import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBarRef, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { MatGridListModule } from '@angular/material/grid-list';
import { ActivatedRoute } from '@angular/router';
import { Firestore } from '@angular/fire/firestore';
import { MockFirestore } from './firebase-mocks';
import { of } from 'rxjs';
import { DomSanitizer } from '@angular/platform-browser';

export const commonTestImports = [
  HttpClientTestingModule,
  RouterTestingModule,
  BrowserAnimationsModule,
  ReactiveFormsModule,
  MatDialogModule,
  MatSnackBarModule,
  MatIconModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatSelectModule,
  MatOptionModule,
  MatExpansionModule,
  MatTabsModule,
  MatGridListModule
];

export const commonTestProviders = [
  { provide: MatDialogRef, useValue: { close: () => {} } },
  { provide: MAT_DIALOG_DATA, useValue: {} },
  { provide: MatSnackBarRef, useValue: { dismiss: () => {} } },
  { provide: MAT_SNACK_BAR_DATA, useValue: {} },
  { provide: ActivatedRoute, useValue: { snapshot: { params: {} } } },
  { provide: Firestore, useClass: MockFirestore },
  { provide: MatIconRegistry, useValue: {
      addSvgIcon: () => {},
      addSvgIconLiteral: () => {},
      getNamedSvgIcon: () => of(''),
      getSvgIconFromUrl: () => of(''),
      getDefaultFontSetClass: () => 'material-icons',
      registerFontClassAlias: () => {},
      className: 'material-icons',
      // Add any other methods your tests might call
    }
  }
];

export function configureTestModule(imports: any[] = [], providers: any[] = []) {
  return TestBed.configureTestingModule({
    imports: [...commonTestImports, ...imports],
    providers: [...commonTestProviders, ...providers]
  });
}

/**
 * Registers dummy SVG icons for common custom icon names in tests.
 * Call this in your test's beforeEach block if your component uses custom icons.
 */
export function registerTestIcons(iconRegistry: MatIconRegistry, sanitizer: DomSanitizer) {
  const dummySvg = '<svg><g></g></svg>';
  const icons = [
    'close-icon',
    'key_icon',
    'test-icon',
    'dollar-circle',
    'arrow-circle-left',
    'presentation_icon',
    'invoicing:card-pos'
  ];
  icons.forEach(icon => {
    iconRegistry.addSvgIconLiteral(icon, sanitizer.bypassSecurityTrustHtml(dummySvg));
  });
} 