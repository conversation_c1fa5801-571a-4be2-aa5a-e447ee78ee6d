import { of } from 'rxjs';

/**
 * Mock implementations for Firebase/Firestore services
 */

export class MockFirestore {
  collection = jasmine.createSpy('collection').and.returnValue({
    doc: jasmine.createSpy('doc').and.returnValue({
      set: jasmine.createSpy('set').and.returnValue(Promise.resolve()),
      update: jasmine.createSpy('update').and.returnValue(Promise.resolve()),
      delete: jasmine.createSpy('delete').and.returnValue(Promise.resolve()),
      get: jasmine.createSpy('get').and.returnValue(Promise.resolve({
        exists: true,
        data: () => ({})
      }))
    }),
    add: jasmine.createSpy('add').and.returnValue(Promise.resolve({ id: 'mock-id' })),
    get: jasmine.createSpy('get').and.returnValue(Promise.resolve({
      docs: []
    }))
  });

  doc = jasmine.createSpy('doc').and.returnValue({
    set: jasmine.createSpy('set').and.returnValue(Promise.resolve()),
    update: jasmine.createSpy('update').and.returnValue(Promise.resolve()),
    delete: jasmine.createSpy('delete').and.returnValue(Promise.resolve()),
    get: jasmine.createSpy('get').and.returnValue(Promise.resolve({
      exists: true,
      data: () => ({})
    }))
  });
}

export const mockCollectionData = jasmine.createSpy('collectionData').and.returnValue(of([]));
export const mockDoc = jasmine.createSpy('doc').and.returnValue({});
export const mockSetDoc = jasmine.createSpy('setDoc').and.returnValue(Promise.resolve());
export const mockUpdateDoc = jasmine.createSpy('updateDoc').and.returnValue(Promise.resolve());
export const mockAddDoc = jasmine.createSpy('addDoc').and.returnValue(Promise.resolve({ id: 'mock-id' }));
export const mockCollection = jasmine.createSpy('collection').and.returnValue({});
export const mockQuery = jasmine.createSpy('query').and.returnValue({});
export const mockOrderBy = jasmine.createSpy('orderBy').and.returnValue({});
export const mockTimestamp = {
  now: jasmine.createSpy('now').and.returnValue({
    toMillis: () => Date.now()
  })
};

/**
 * Mock Firebase Auth
 */
export class MockAuth {
  currentUser = null;
  signInWithEmailAndPassword = jasmine.createSpy('signInWithEmailAndPassword').and.returnValue(
    Promise.resolve({
      user: { uid: 'mock-uid', email: '<EMAIL>' }
    })
  );
  signOut = jasmine.createSpy('signOut').and.returnValue(Promise.resolve());
  onAuthStateChanged = jasmine.createSpy('onAuthStateChanged').and.returnValue(() => {});
}

/**
 * Mock Angular Fire services
 */
export const mockAngularFireAuth = {
  authState: of(null),
  signInWithEmailAndPassword: jasmine.createSpy('signInWithEmailAndPassword').and.returnValue(
    Promise.resolve({
      user: { uid: 'mock-uid', email: '<EMAIL>' }
    })
  ),
  signOut: jasmine.createSpy('signOut').and.returnValue(Promise.resolve())
};

export const mockAngularFirestore = {
  collection: jasmine.createSpy('collection').and.returnValue({
    doc: jasmine.createSpy('doc').and.returnValue({
      set: jasmine.createSpy('set').and.returnValue(Promise.resolve()),
      update: jasmine.createSpy('update').and.returnValue(Promise.resolve()),
      delete: jasmine.createSpy('delete').and.returnValue(Promise.resolve()),
      valueChanges: jasmine.createSpy('valueChanges').and.returnValue(of({}))
    }),
    add: jasmine.createSpy('add').and.returnValue(Promise.resolve({ id: 'mock-id' })),
    valueChanges: jasmine.createSpy('valueChanges').and.returnValue(of([]))
  }),
  doc: jasmine.createSpy('doc').and.returnValue({
    set: jasmine.createSpy('set').and.returnValue(Promise.resolve()),
    update: jasmine.createSpy('update').and.returnValue(Promise.resolve()),
    delete: jasmine.createSpy('delete').and.returnValue(Promise.resolve()),
    valueChanges: jasmine.createSpy('valueChanges').and.returnValue(of({}))
  })
};

/**
 * Helper function to create mock Firestore timestamp
 */
export function createMockTimestamp(date: Date = new Date()) {
  return {
    toDate: () => date,
    toMillis: () => date.getTime(),
    seconds: Math.floor(date.getTime() / 1000),
    nanoseconds: (date.getTime() % 1000) * 1000000
  };
}

/**
 * Helper function to create mock Firestore document
 */
export function createMockFirestoreDoc(data: any, id: string = 'mock-id') {
  return {
    id,
    data: () => data,
    exists: true,
    ref: {
      id,
      path: `collection/${id}`
    }
  };
}

/**
 * Helper function to create mock Firestore collection
 */
export function createMockFirestoreCollection(docs: any[] = []) {
  return {
    docs: docs.map((doc, index) => createMockFirestoreDoc(doc, `doc-${index}`)),
    size: docs.length,
    empty: docs.length === 0,
    forEach: (callback: (doc: any, index: number, array: any[]) => void) => docs.forEach(callback)
  };
}
