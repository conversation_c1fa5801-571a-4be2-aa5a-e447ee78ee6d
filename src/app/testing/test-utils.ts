import { of, BehaviorSubject } from 'rxjs';
import { User } from '@modules/auth/models/user.model';

/**
 * Mock implementations for commonly used services in tests
 */

export class MockAuthService {
  private userSubject = new BehaviorSubject<User | null>(null);
  
  userData = this.userSubject.asObservable();

  getUserData() {
    return this.userSubject.getValue();
  }

  setUserData(user: User | null) {
    this.userSubject.next(user);
  }

  login = jasmine.createSpy('login').and.returnValue(Promise.resolve({ status_code: 200 }));
  logout = jasmine.createSpy('logout').and.returnValue(Promise.resolve());
  isAuthenticated = jasmine.createSpy('isAuthenticated').and.returnValue(true);
  refreshToken = jasmine.createSpy('refreshToken').and.returnValue(of({ access: 'mock-token' }));
  initializeAuthUser = jasmine.createSpy('initializeAuthUser').and.returnValue(Promise.resolve('1'));
  redirectAfterAuth = jasmine.createSpy('redirectAfterAuth');
}

export class MockDataService {
  get = jasmine.createSpy('get').and.returnValue(Promise.resolve({ status_code: 200, data: {} }));
  post = jasmine.createSpy('post').and.returnValue(Promise.resolve({ status_code: 200, data: {} }));
  put = jasmine.createSpy('put').and.returnValue(Promise.resolve({ status_code: 200, data: {} }));
  delete = jasmine.createSpy('delete').and.returnValue(Promise.resolve({ status_code: 200, data: {} }));
}

export class MockUtilsService {
  showProgressDialog = jasmine.createSpy('showProgressDialog');
  closeProgressDialog = jasmine.createSpy('closeProgressDialog');
  openSnackBar = jasmine.createSpy('openSnackBar');
  isMobile = jasmine.createSpy('isMobile').and.returnValue(false);
  isDesktop = jasmine.createSpy('isDesktop').and.returnValue(true);
  isTablet = jasmine.createSpy('isTablet').and.returnValue(false);
  isValidToken = jasmine.createSpy('isValidToken').and.returnValue(true);
  decodeToken = jasmine.createSpy('decodeToken').and.returnValue({ exp: Date.now() / 1000 + 3600 });
  saveAuthToken = jasmine.createSpy('saveAuthToken');
  clearAuth = jasmine.createSpy('clearAuth');
  setTitle = jasmine.createSpy('setTitle');
  handleAll = false;
  getAPIServerUrl = jasmine.createSpy('getAPIServerUrl').and.returnValue('http://localhost:8000');
  checkDate = jasmine.createSpy('checkDate').and.returnValue('valid');
  isNewDate = jasmine.createSpy('isNewDate').and.returnValue(true);
}

export class MockChatService {
  private chatsSubject = new BehaviorSubject<any[]>([]);
  private chatMessagesSubject = new BehaviorSubject<any[]>([]);
  private selectedDriverSubject = new BehaviorSubject<any>(null);
  private unreadChatSubject = new BehaviorSubject<boolean>(false);

  chats$ = this.chatsSubject.asObservable();
  chatMessages$ = this.chatMessagesSubject.asObservable();
  selectedDriver$ = this.selectedDriverSubject.asObservable();
  unreadChat$ = this.unreadChatSubject.asObservable();

  initChatStream = jasmine.createSpy('initChatStream').and.returnValue(of([]));
  getDriverChat = jasmine.createSpy('getDriverChat');
  updateDriverChat = jasmine.createSpy('updateDriverChat').and.returnValue(Promise.resolve());
  markChatAsRead = jasmine.createSpy('markChatAsRead').and.returnValue(Promise.resolve());
  createChatMessage = jasmine.createSpy('createChatMessage').and.returnValue(Promise.resolve());
  getDriver = jasmine.createSpy('getDriver').and.returnValue(of({}));
  searchDrivers = jasmine.createSpy('searchDrivers').and.returnValue(of([]));
  getChatList = jasmine.createSpy('getChatList').and.returnValue(of([]));
}

export class MockWebsocketService {
  messages = of({});
  connect = jasmine.createSpy('connect').and.returnValue(of({}));
}

export class MockNotificationService {
  private newNotificationSubject = new BehaviorSubject<boolean>(false);
  
  newNotification$ = this.newNotificationSubject.asObservable();
  notificationStream$ = of([]);
  totalCount = 0;
  notificationsOpened = false;

  triggerNotificationLoad = jasmine.createSpy('triggerNotificationLoad');
  readNotification = jasmine.createSpy('readNotification').and.returnValue(Promise.resolve());
}

/**
 * Helper functions for creating test data
 */
export function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: 1,
    first_name: 'Test',
    last_name: 'User',
    email: '<EMAIL>',
    type: 'ADMIN',
    user_permissions: [],
    ...overrides
  } as User;
}

export function createMockPermission(permission_on: string, title: string = 'Test Permission') {
  return {
    permission_id: 1,
    permission: {
      permission_on,
      title
    },
    method_allowed: ['GET', 'POST', 'PUT', 'DELETE'] as ('GET' | 'DELETE' | 'POST' | 'PUT')[]
  };
}

export function createMockReservation(overrides: any = {}) {
  return {
    id: 1,
    status: 'PENDING',
    pickup_time: new Date().toISOString(),
    dropoff_time: new Date().toISOString(),
    customer: { id: 1, first_name: 'John', last_name: 'Doe' },
    driver: null,
    vehicle_type: 1,
    ...overrides
  };
}

export function createMockDriver(overrides: any = {}) {
  return {
    id: 1,
    first_name: 'Driver',
    last_name: 'Test',
    email: '<EMAIL>',
    phone: '+1234567890',
    status: 'ACTIVE',
    ...overrides
  };
}

export function createMockVehicle(overrides: any = {}) {
  return {
    id: 1,
    code: 'VEH001',
    make: 'Toyota',
    model: 'Camry',
    year: '2022',
    status: 'ACTIVE',
    capacity: 4,
    ...overrides
  };
}

/**
 * Common test module configurations
 */
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBarModule, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { ActivatedRoute } from '@angular/router';

export const commonTestImports = [
  HttpClientTestingModule,
  BrowserAnimationsModule,
  RouterTestingModule,
  MatDialogModule,
  MatSnackBarModule,
  MatIconModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatSelectModule,
  MatDatepickerModule,
  MatNativeDateModule,
  MatDividerModule,
  MatExpansionModule
];

export const commonTestProviders = [
  {
    provide: ActivatedRoute,
    useValue: {
      params: of({}),
      queryParams: of({}),
      snapshot: { params: {}, queryParams: {} }
    }
  },
  {
    provide: MAT_DIALOG_DATA,
    useValue: {}
  },
  {
    provide: MatDialogRef,
    useValue: {
      close: jasmine.createSpy('close'),
      afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of({}))
    }
  },
  {
    provide: MAT_SNACK_BAR_DATA,
    useValue: {}
  }
];
