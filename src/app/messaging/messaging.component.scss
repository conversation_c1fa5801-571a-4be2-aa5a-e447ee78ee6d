.col-in-12 {
  width: 96%;
  padding: 0 2%;
  margin: 0 auto;
  height: 100%;
}
.col-in-12.action-row {
  width: 100%;
  padding: 0;
  margin: 0 auto;
  height: 100%;
}
.col-in-6 {
  float:right;
  width: 48%;
  margin: 0 1%;
}
.col-in-2{
  width: 6%;
  float: right;
  margin: 1%;
}
.col-in-2 button{
  min-width: auto;
}
.col-md-10 {
  margin: 0 auto;
  float: none;
  width:90%;
}
.pt-50 {
  padding: 54px 0;
}
.w-90{
  width:80%;
}
.w-10{
  width: 20%;
}
.w-20{
  width: 19%;
  display: inline-flex;
}
table th {
  padding: 15px;
  border-bottom: 1px solid;
}
.col-md-3{
  width: 32.3%;
  margin: 0 0.5% ;
  float: right;
}
.col-in-8{
  width: 74%;
  margin: 0 0.5%;
  float: right;
}
.form-group.col-md-4 {
  margin: 0 0.5%;
  width: 24%;
}
table, tr, tbody {
  border-spacing: 0;
}
td.mat-cell.cdk-cell {
  padding: 20px 5px;
  border-bottom: 1px solid #ddd;
}
table.col-md-12.mat-table.cdk-table {
  box-shadow: 0 0 1px 0;
}
.mat-action-row {
  padding: 0;
}
.action-row-href{
  cursor:pointer;
  padding:10px 0;
  float:right;
  clear:both;
  width:85%;
}
.list-items-navigator{
  padding: 10px 2%;
  display: flex;
  border-bottom: 1px solid #ddd;
}
mat-expansion-panel.mat-expansion-panel {
  margin: 0;
}
mat-tab-body, .mat-expansion-panel {
  background-color: #fafafa;
}
.col-md-12{
  justify-content: center;
  align-items: center;
  display: flex;
  float: none;
}
.list-items-navigator .hidden, .list-items-navigator .hidden {
  display: none;
}
.list-items-navigator:hover .hidden, .list-items-navigator.selected .hidden {
  display: block;
}
.list-items-navigator:hover, .list-items-navigator.selected{
  background-color: #e0e0e0;
}
.border{
  border: 1px solid #dcdcdc;
}
.flex-image{
  flex: 1 1;
  flex-wrap: wrap;
  flex-flow: column;
}
::ng-deep {
  button.mat-focus-indicator.mat-button.mat-button-base.icon-button {
    min-width: 25px!important;
  }
  .tooltip-date {
    background-color: rgba(0,0,0,0.54);
    font-size: 14px;
  }
}
.col-in-12.right-flex {
  overflow-y: scroll;
}
.col-md-4.align-right.cover-image {
  margin-left: 15px;
  min-height: 65px;
  width: 65px;
  border-radius: 100%;
  padding: 5px;
}
mat-card.mat-card.card-driver {
  border-bottom: 1px solid #e0e0e0;
  border-radius: 0;
  padding: 5px;
  background-color: #fff;
  &.selected-driver{
    background-color: #F2F2F2;
  }
  &.unread{
    background-color: rgba(206,176,124,0.2);
  }
}
.col-md-8 {
  h2 {
    margin: 10px 0 0;
  }
  p {
    color: #6c6c6c;
  }
}
.message-header{
  display: flex;
  gap: 20px;
  align-items: center;
  height: 82px;
  padding: 10px 2% 10px;
  border-bottom: 1px solid #e0e0e0;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
}
.message-box {
  width:98%;
  height: calc(100vh - 210px);
  padding: 0 2% 0;
  overflow: hidden;
  overflow-y: scroll;
  margin-top: 20px;
  box-sizing: border-box;
}
.message-footer{
  border-top: 1px solid #e0e0e0;
  height: 65px;
  padding: 2%;
  width: 96%;
  background-color: #fff;
}
.form-group.col-in-10 {
  width: 90%;
  display: inline-flex;
}
.message{
  &.col-in-12,.col-in-12{
    padding-left: 0;
    padding-right: 0;
    width: 100%;
  }
  .col-md-4.align-right.cover-image {
    min-height: 35px;
    width: 35px;
  }
}
.staff-messages{
  .sender,.received{
    margin: 4px 16px 16px 8px;
    padding: 11.5px 12px 9.5px;
    border-radius: 17.5px;
    float: right;
    clear: both;
    color: #fff;
    background-color: #007CC4;
    max-width: 45%;
    overflow-wrap: anywhere;
  }
  .received{
    color: #000;
    background-color: #F9F9F9;
    float: left;
  }
}
.col-in-12.right-flex {
  border-top: 1px solid #e0e0e0;
  width: 100%;
  padding: 0;
  border-right: 1px solid #e0e0e0;
}
div#message-box {
  display: flex;
  float: left;
  clear: both;
}
.card-driver .w-full {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start
}
.card-driver {
  border: none;
  box-shadow: none;
  border-bottom: 1px solid #DFE9F5;
  border-radius: 0;
  padding: 10px;
}
.selected-driver{
  background-color: #F2F2F2;
}
.driver-chats{
  display: flex;
  border-bottom: 1px solid #e0e9f6;
  padding: 0 12px;
}
.w-full.hover-image.flex.center {
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
}
span {
  font-size: 12px;
  color: #808FA4;
}
h2.active {
  margin-left:5px;
  line-height: 25px;
}
span.date-message {
  float: right;
}
.sender span.date-message {
  color:#fff;
}
.send_sms_icon .mat-icon{
  width: 30px;
  height: 30px;
}
.overflow-last-message{
  text-overflow: ellipsis;
  overflow: hidden;
  width: 95%;
  height: 25px;
  white-space: nowrap;
  float: left;
}
.overflow-last-message.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content {
  right: 0!important;
}
.w-full.center {
  height: 90vh;
  overflow-y: scroll;
}
.w-full.input-field {
  width: 98% !important;
  margin: 3% 1% 0 1%;
}
.driver-image{
  border-radius: 100%;
}
::ng-deep .tooltip-date {
  border-radius: 5px;
}

::ng-deep .messaging-grid .mat-grid-tile-content {
  flex-direction: column;
}