import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Firestore } from '@angular/fire/firestore';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { MessagingComponent } from './messaging.component';
import { ChatService } from '@core/services/chat/chat.service';
import { AuthService } from '@modules/auth/services/auth.service';
import { DataService, UtilsService } from '@core/services';
import { MockAuthService, MockUtilsService } from '../testing/test-utils';
import { MockFirestore } from '../testing/firebase-mocks';

describe('MessagingComponent', () => {
  let component: MessagingComponent;
  let fixture: ComponentFixture<MessagingComponent>;
  let mockAuthService: MockAuthService;
  let mockUtilsService: MockUtilsService;
  let mockDataService: jasmine.SpyObj<DataService>;

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['get', 'post']);
    mockAuthService = new MockAuthService();
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      declarations: [ MessagingComponent ],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatFormFieldModule,
        MatInputModule
      ],
      providers: [
        ChatService,
        { provide: Firestore, useClass: MockFirestore },
        { provide: AuthService, useValue: mockAuthService },
        { provide: DataService, useValue: dataServiceSpy },
        { provide: UtilsService, useValue: mockUtilsService }
      ]
    })
    .compileComponents();

    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockDataService.get.and.returnValue(Promise.resolve({ status_code: 200, data: [] }));

    fixture = TestBed.createComponent(MessagingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
