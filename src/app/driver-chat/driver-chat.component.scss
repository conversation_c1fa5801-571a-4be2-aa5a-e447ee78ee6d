.driver-chats {
  display: flex;
  box-sizing: border-box;
  min-height: calc(100vh - 65px);

  > * {
    flex: 1;
  }

  .landing-view {
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background-color: #F9F9F9;
    border-left: 1px solid #DFE9F5;

    .mat-icon {
      width: 250px;
      max-width: 80%;
      margin-bottom: 1rem;
      height: auto;
    }

    > p {
      text-align: center;
      margin-bottom: 0;
      font-weight: 500;
      font-size: 16px;
      color: #808FA4;
    }
  }

  .chat-view {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #DFE9F5;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #DFE9F5;
      padding: 1rem 1.5rem 0.75rem;

      .driver-info {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;

        .driver-avatar {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background-color: #808fa4;
          border: 1px solid #DFE9F5;
        }
      }

      .driver-actions {
        display: flex;
        gap: 0.5rem;

        .active-trip {
          align-self: center;
          font-weight: 500;
          color: #808fa4;
        }

        .mat-mdc-icon-button {
          padding: 8px;
          --mdc-icon-button-state-layer-size: 40px;
        }
      }
    }

    &__messages {
      //flex: 1;
      position: relative;
      padding: 1rem 1rem 0;
      height: calc(100vh - 65px - 71px - 74px);
      scroll-behavior: smooth;
      box-sizing: border-box;
      overflow-y: scroll;

      .message-row {
        display: flex;
        flex-direction: column;
        margin-bottom: 0.75rem;
        padding-inline: 10px;
        position: relative;

        .message {
          padding: 12px 12px 12px 8px;
          min-width: 60px;
          max-width: 75%;

          &-in {
            align-self: flex-start;
            background-color: #F9F9F9;
            border-radius: 12px 12px 12px 0;

            ~ .message-tail {
              left: 0;
            }

            .time {
              color: #808FA4;
            }
          }

          &-out {
            color: #FFFFFF;
            align-self: flex-end;
            background-color: #007CC4;
            border-radius: 12px 12px 0;

            .time {
              color: #DFE9F5;
            }

            ~ .message-tail {
              right: 0;
              transform: rotateY(-180deg);

              > .mat-icon {
                ::ng-deep svg > path {
                  fill: #007cc4;
                }
              }
            }
          }

          .time {
            display: block;
            text-align: end;
            margin-top: 0.25rem;
          }

          &-tail {
            position: absolute;
            bottom: -9px;

            > .mat-icon {
              width: 23px;
              height: 19px;
            }
          }

          &.pulse {
            display: block;
            height: 20px;
            width: 35%;
            animation: pulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
            animation-delay: 0.5s;
          }
        }
      }

      .message-date {
        display: flex;
        align-items: center;
        margin-block: 0.5rem;
        font-weight: 500;
        font-size: 12px;
        color: #808FA4;
      }
    }

    &__input {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      background-color: #F9F9F9;
      border-color: #DFE9F5;
      border-width: 1px 0;
      border-style: solid;
      gap: 0.5rem;

      ::ng-deep .mat-form-field-appearance-outline {
        .mat-mdc-text-field-wrapper {
          background-color: #FFFFFF;
          border-radius: 24px;
        }

        .mdc-text-field--outlined {
          --mdc-outlined-text-field-container-shape: 24px !important;
        }
      }
    }
  }
}

:host ::ng-deep {
  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
}
