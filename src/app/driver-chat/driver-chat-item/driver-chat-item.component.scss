:host {
  --skeleton-rect-width: 100%;
  --skeleton-rect-height: 1em;
  --skeleton-rect-radius: 12px;
}

.chat-item {
  display: flex;
  align-items: center;
  padding-block: 0.5rem;
  padding-inline: 0.5rem 0.5rem;
  //margin-inline: -0.5rem -0.5rem;
  border-radius: 12px;
  cursor: pointer;
  gap: 0.5rem;

  &__avatar {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    box-sizing: border-box;
    background-color: #808fa4;
    border: 1px solid #DFE9F5;
    --skeleton-rect-width: 55px;
    --skeleton-rect-height: 55px;
    --skeleton-rect-radius: 50%;
  }

  &.opened {
    color: #007cc4;
    background-color: #E6F4FC;
  }

  &.opened .chat-item__avatar {
    border-radius: 8px;
  }

  &__details {
    flex: 1;

    .chat-date, .last-message {
      font-weight: 500;
      color: #808fa4;
    }

    .last-message {
      display: -moz-box;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      font-size: 14px;

      &.unread {
        position: relative;
        padding-inline-end: 10px;

        :after {
          content: '';
          position: absolute;
          top: 50%;
          right: 0;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          transform: translateY(-50%);
          background-color: #FF3B30;
        }
      }

      .sender {
        color: #627186;
      }
    }
  }

  &:not(.opened):hover {
    background-color: #F2F2F2;
  }

  .pulse {
    display: block;
    width: var(--skeleton-rect-width);
    height: var(--skeleton-rect-height);
    border-radius: var(--skeleton-rect-radius);
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    background: rgb(239, 241, 246) no-repeat;
    animation-delay: 0.5s;
  }
}
