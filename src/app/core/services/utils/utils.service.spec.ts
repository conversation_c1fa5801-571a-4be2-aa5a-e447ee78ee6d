import { TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeviceDetectorService } from 'ngx-device-detector';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { UtilsService } from './utils.service';
import { environment } from '@env/environment';

describe('UtilsService', () => {
  let service: UtilsService;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;
  let mockDeviceService: jasmine.SpyObj<DeviceDetectorService>;

  beforeEach(() => {
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open', 'close']);
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['openFromComponent']);
    const deviceSpy = jasmine.createSpyObj('DeviceDetectorService', ['isMobile', 'isDesktop', 'isTablet']);

    TestBed.configureTestingModule({
      imports: [BrowserAnimationsModule],
      providers: [
        UtilsService,
        { provide: MatDialog, useValue: dialogSpy },
        { provide: MatSnackBar, useValue: snackBarSpy },
        { provide: DeviceDetectorService, useValue: deviceSpy }
      ]
    });

    service = TestBed.inject(UtilsService);
    mockDialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;
    mockDeviceService = TestBed.inject(DeviceDetectorService) as jasmine.SpyObj<DeviceDetectorService>;
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('token management', () => {
    it('should save auth token to localStorage', () => {
      const token = 'test-token';
      const refresh = 'refresh-token';

      service.saveAuthToken(token, refresh);

      expect(localStorage.getItem('token')).toBe(token);
      expect(localStorage.getItem('refresh')).toBe(refresh);
    });

    it('should save only access token when refresh not provided', () => {
      const token = 'test-token';

      service.saveAuthToken(token);

      expect(localStorage.getItem('token')).toBe(token);
      expect(localStorage.getItem('refresh')).toBeNull();
    });

    it('should clear auth data from localStorage', () => {
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('refresh', 'refresh-token');
      localStorage.setItem('userId', '123');

      service.clearAuth();

      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('refresh')).toBeNull();
      expect(localStorage.getItem('userId')).toBeNull();
    });
  });

  describe('token validation', () => {
    it('should return true for valid token', () => {
      // Create a valid JWT token with future expiration
      const futureExp = Math.floor(Date.now() / 1000) + 3600;
      const validToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.${btoa(JSON.stringify({ exp: futureExp }))}.fake`;

      const result = service.isValidToken(validToken);

      expect(result).toBe(true);
    });

    it('should return false for expired token', () => {
      // Create an expired JWT token
      const pastExp = Math.floor(Date.now() / 1000) - 3600;
      const expiredToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.${btoa(JSON.stringify({ exp: pastExp }))}.fake`;

      const result = service.isValidToken(expiredToken);

      expect(result).toBe(false);
    });

    it('should return false for null token', () => {
      const result = service.isValidToken(null);

      expect(result).toBe(false);
    });

    it('should return false for malformed token', () => {
      const malformedToken = 'invalid-token';

      const result = service.isValidToken(malformedToken);

      expect(result).toBe(false);
    });
  });

  describe('device detection', () => {
    it('should return true for mobile when device is mobile', () => {
      mockDeviceService.isMobile.and.returnValue(true);
      mockDeviceService.isTablet.and.returnValue(false);

      const result = service.isMobile();

      expect(result).toBe(true);
    });

    it('should return true for mobile when device is tablet', () => {
      mockDeviceService.isMobile.and.returnValue(false);
      mockDeviceService.isTablet.and.returnValue(true);

      const result = service.isMobile();

      expect(result).toBe(true);
    });

    it('should return false for mobile when device is desktop', () => {
      mockDeviceService.isMobile.and.returnValue(false);
      mockDeviceService.isTablet.and.returnValue(false);

      const result = service.isMobile();

      expect(result).toBe(false);
    });

    it('should return desktop detection result', () => {
      mockDeviceService.isDesktop.and.returnValue(true);

      const result = service.isDesktop();

      expect(result).toBe(true);
      expect(mockDeviceService.isDesktop).toHaveBeenCalled();
    });

    it('should return tablet detection result', () => {
      mockDeviceService.isTablet.and.returnValue(true);

      const result = service.isTablet();

      expect(result).toBe(true);
      expect(mockDeviceService.isTablet).toHaveBeenCalled();
    });
  });

  describe('snackbar', () => {
    it('should open snackbar with correct parameters', () => {
      const message = 'Test message';
      const duration = 5000;
      const status = 'success';

      service.openSnackBar(message, duration, status);

      expect(mockSnackBar.openFromComponent).toHaveBeenCalledWith(
        jasmine.any(Function),
        {
          duration,
          panelClass: status,
          data: {
            message,
            status
          }
        }
      );
    });
  });

  describe('environment methods', () => {
    it('should return API server URL from environment', () => {
      const result = service.getAPIServerUrl();
      expect(result).toBe(environment.api_url);
    });

    it('should return main website URL from environment', () => {
      const result = service.mainWebsite();
      expect(result).toBe(environment.main_websitelink);
    });

    it('should return protocol from environment', () => {
      const result = service.protocol();
      expect(result).toBe(environment.protocol);
    });

    it('should return page size from environment', () => {
      const result = service.pageSize();
      expect(result).toBe(environment.page_size);
    });
  });

  describe('date utilities', () => {
    it('should return "expired" for past dates', () => {
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // yesterday

      const result = service.checkDate(pastDate);

      expect(result).toBe('expired');
    });

    it('should return "nearly" for dates within 7 days', () => {
      const nearDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(); // 3 days from now

      const result = service.checkDate(nearDate);

      expect(result).toBe('nearly');
    });

    it('should return "valid" for future dates beyond 7 days', () => {
      const futureDate = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(); // 10 days from now

      const result = service.checkDate(futureDate);

      expect(result).toBe('valid');
    });

    it('should return true for future dates', () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // tomorrow

      const result = service.isNewDate(futureDate);

      expect(result).toBe(true);
    });

    it('should return false for past dates', () => {
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // yesterday

      const result = service.isNewDate(pastDate);

      expect(result).toBe(false);
    });
  });

  describe('progress dialog', () => {
    it('should open progress dialog', () => {
      const mockDialogRef = { close: jasmine.createSpy('close') };
      mockDialog.open.and.returnValue(mockDialogRef as any);

      service.showProgressDialog();

      expect(mockDialog.open).toHaveBeenCalled();
    });

    it('should close progress dialog', () => {
      const mockDialogRef = { close: jasmine.createSpy('close') };
      mockDialog.open.and.returnValue(mockDialogRef as any);

      // Open dialog first
      service.showProgressDialog();

      // Then close it
      service.closeProgressDialog();

      expect(mockDialog.open).toHaveBeenCalled();
    });

    it('should handle multiple dialog open/close calls', () => {
      const mockDialogRef = { close: jasmine.createSpy('close') };
      mockDialog.open.and.returnValue(mockDialogRef as any);

      // Open multiple times
      service.showProgressDialog();
      service.showProgressDialog();

      // Close multiple times
      service.closeProgressDialog();
      service.closeProgressDialog();

      expect(mockDialog.open).toHaveBeenCalled();
    });
  });
});
