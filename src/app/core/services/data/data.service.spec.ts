import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Router } from '@angular/router';

import { DataService } from './data.service';
import { AuthService } from '@modules/auth/services/auth.service';
import { UtilsService } from '@core/services';
import { MockAuthService, MockUtilsService } from '../../../testing/test-utils';

describe('DataService', () => {
  let service: DataService;
  let httpMock: HttpTestingController;
  let mockAuthService: MockAuthService;
  let mockUtilsService: MockUtilsService;

  beforeEach(() => {
    mockAuthService = new MockAuthService();
    mockUtilsService = new MockUtilsService();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        DataService,
        { provide: AuthService, useValue: mockAuthService },
        { provide: UtilsService, useValue: mockUtilsService }
      ]
    });

    service = TestBed.inject(DataService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('GET requests', () => {
    it('should make GET request and handle successful response', async () => {
      const apiUrl = 'test-endpoint';
      const mockResponse = { status_code: 200, data: { message: 'success' } };

      const resultPromise = service.get(apiUrl);

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await resultPromise;
      expect(result).toEqual(mockResponse);
      expect(mockUtilsService.showProgressDialog).toHaveBeenCalled();
      expect(mockUtilsService.closeProgressDialog).toHaveBeenCalled();
    });

    it('should make GET request without showing progress when hideProgress is true', async () => {
      const apiUrl = 'test-endpoint';
      const mockResponse = { status_code: 200, data: { message: 'success' } };

      const resultPromise = service.get(apiUrl, true);

      const req = httpMock.expectOne(apiUrl);
      req.flush(mockResponse);

      await resultPromise;
      expect(mockUtilsService.showProgressDialog).not.toHaveBeenCalled();
    });

    it('should handle 401 error and call auth logout', async () => {
      const apiUrl = 'test-endpoint';

      const resultPromise = service.get(apiUrl);

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Unauthorized'), { status: 401 });

      const result = await resultPromise;
      expect(result.hasError).toBe(true);
      expect(result.status_code).toBe(401);
      expect(mockUtilsService.closeProgressDialog).toHaveBeenCalled();
    });

    it('should handle 403 error and show snackbar', async () => {
      const apiUrl = 'test-endpoint';
      const errorResponse = {
        data: { message: 'Forbidden access' }
      };

      const resultPromise = service.get(apiUrl);

      const req = httpMock.expectOne(apiUrl);
      req.flush(errorResponse, { status: 403, statusText: 'Forbidden' });

      const result = await resultPromise;
      expect(result.hasError).toBe(true);
      expect(result.status_code).toBe(403);
    });

    it('should handle 409 conflict error with field errors', async () => {
      const apiUrl = 'test-endpoint';
      const errorResponse = {
        data: {
          fields_error: {
            email: ['Email already exists']
          }
        }
      };

      const resultPromise = service.get(apiUrl);

      const req = httpMock.expectOne(apiUrl);
      req.flush(errorResponse, { status: 409, statusText: 'Conflict' });

      const result = await resultPromise;
      expect(result.hasError).toBe(true);
      expect(result.status_code).toBe(409);
    });
  });

  describe('POST requests', () => {
    it('should make POST request and handle successful response', async () => {
      const apiUrl = 'test-endpoint';
      const requestBody = { name: 'test' };
      const mockResponse = { status_code: 201, data: { id: 1, name: 'test' } };

      const resultPromise = service.post(apiUrl, requestBody);

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(requestBody);
      req.flush(mockResponse);

      const result = await resultPromise;
      expect(result).toEqual(mockResponse);
    });

    it('should handle POST error', async () => {
      const apiUrl = 'test-endpoint';
      const requestBody = { name: 'test' };

      const resultPromise = service.post(apiUrl, requestBody);

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server Error'), { status: 500 });

      const result = await resultPromise;
      expect(result.hasError).toBe(true);
      expect(result.status_code).toBe(500);
    });
  });
});
