import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Firestore } from '@angular/fire/firestore';
import { of } from 'rxjs';

import { ChatService } from './chat.service';
import { AuthService } from '@modules/auth/services/auth.service';
import { UtilsService } from '@core/services';
import { MockAuthService, MockUtilsService, createMockUser, createMockPermission } from '../../../testing/test-utils';

describe('ChatService', () => {
  let service: ChatService;
  let httpMock: HttpTestingController;
  let mockAuthService: MockAuthService;
  let mockUtilsService: MockUtilsService;
  let mockFirestore: jasmine.SpyObj<Firestore>;

  beforeEach(() => {
    const firestoreSpy = jasmine.createSpyObj('Firestore', ['collection', 'doc']);
    mockAuthService = new MockAuthService();
    mockUtilsService = new MockUtilsService();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ChatService,
        { provide: Firestore, useValue: firestoreSpy },
        { provide: AuthService, useValue: mockAuthService },
        { provide: UtilsService, useValue: mockUtilsService }
      ]
    });

    service = TestBed.inject(ChatService);
    httpMock = TestBed.inject(HttpTestingController);
    mockFirestore = TestBed.inject(Firestore) as jasmine.SpyObj<Firestore>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('observables', () => {
    it('should provide chats observable', (done) => {
      service.chats$.subscribe(chats => {
        expect(chats).toBeNull(); // Initial value
        done();
      });
    });

    it('should provide chat messages observable', (done) => {
      service.chatMessages$.subscribe(messages => {
        expect(messages).toBeNull(); // Initial value
        done();
      });
    });

    it('should provide selected driver observable', (done) => {
      service.selectedDriver.subscribe(driver => {
        expect(driver).toBeNull(); // Initial value
        done();
      });
    });

    it('should provide unread chat observable', (done) => {
      service.unreadChat$.subscribe(hasUnread => {
        expect(hasUnread).toBe(false); // Initial value
        done();
      });
    });
  });

  describe('initChatStream', () => {
    it('should initialize chat stream for admin user on desktop', (done) => {
      const adminUser = createMockUser({ type: 'ADMIN' });
      mockAuthService.setUserData(adminUser);
      mockUtilsService.isMobile.and.returnValue(false);

      // Mock getChatList to return observable
      spyOn(service, 'getChatList').and.returnValue(of([]));

      service.initChatStream().subscribe(result => {
        expect(result).toEqual([]);
        expect(service.getChatList).toHaveBeenCalled();
        done();
      });
    });

    it('should not initialize chat stream for mobile users', (done) => {
      const adminUser = createMockUser({ type: 'ADMIN' });
      mockAuthService.setUserData(adminUser);
      mockUtilsService.isMobile.and.returnValue(true);

      spyOn(service, 'getChatList').and.returnValue(of([]));

      service.initChatStream().subscribe({
        next: () => {
          fail('Should not emit for mobile users');
        },
        complete: () => {
          expect(service.getChatList).not.toHaveBeenCalled();
          done();
        }
      });
    });

    it('should initialize chat stream for user with driver permissions', (done) => {
      const user = createMockUser({
        type: 'USER',
        user_permissions: [
          createMockPermission('admin/drivers', 'Driver Management')
        ]
      });
      mockAuthService.setUserData(user);
      mockUtilsService.isMobile.and.returnValue(false);

      spyOn(service, 'getChatList').and.returnValue(of([]));

      service.initChatStream().subscribe(result => {
        expect(result).toEqual([]);
        expect(service.getChatList).toHaveBeenCalled();
        done();
      });
    });
  });

  describe('getDriver', () => {
    it('should fetch driver data successfully', (done) => {
      const driverId = '123';
      const mockDriverData = {
        status_code: 200,
        data: { id: 123, first_name: 'John', last_name: 'Doe' }
      };

      service.getDriver(driverId).subscribe(result => {
        expect(result).toEqual(mockDriverData.data);
        done();
      });

      const req = httpMock.expectOne(`admin/drivers/${driverId}?dump=1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockDriverData);
    });

    it('should handle driver fetch error', (done) => {
      const driverId = '123';

      service.getDriver(driverId).subscribe(result => {
        expect(result).toBeNull();
        done();
      });

      const req = httpMock.expectOne(`admin/drivers/${driverId}?dump=1`);
      req.error(new ErrorEvent('Network error'), { status: 404 });
    });

    it('should return null for non-200 status code', (done) => {
      const driverId = '123';
      const mockResponse = {
        status_code: 404,
        data: { message: 'Driver not found' }
      };

      service.getDriver(driverId).subscribe(result => {
        expect(result).toBeNull();
        done();
      });

      const req = httpMock.expectOne(`admin/drivers/${driverId}?dump=1`);
      req.flush(mockResponse);
    });
  });

  describe('setSelectedDriver', () => {
    it('should set selected driver', (done) => {
      const mockDriver = {
        id: 1,
        first_name: 'John',
        last_name: 'Doe',
        status: 'ACTIVE',
        active_trip: null,
        email: '<EMAIL>',
        phone_number: '+1234567890',
        image: null
      };

      service.selectedDriver = mockDriver as any;

      service.selectedDriver.subscribe(driver => {
        expect(driver).toBeDefined();
        done();
      });
    });

    it('should clear selected driver', (done) => {
      service.selectedDriver = null;

      service.selectedDriver.subscribe(driver => {
        expect(driver).toBeNull();
        done();
      });
    });
  });

  describe('searchDrivers', () => {
    it('should search drivers and categorize results', (done) => {
      const searchTerm = 'john';
      const mockResponse = {
        status_code: 200,
        data: [
          { id: 1, first_name: 'John', last_name: 'Doe' },
          { id: 2, first_name: 'Jane', last_name: 'Smith' }
        ]
      };

      service.searchDrivers(searchTerm).subscribe(result => {
        expect(result.length).toBe(2);
        expect(result[0]).toBeDefined();
        expect(result[1]).toBeDefined();
        done();
      });

      const req = httpMock.expectOne(`admin/drivers?search=${searchTerm}&status=ACTIVE&dump=1&page_size=50`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should return empty array for non-200 status code', (done) => {
      const searchTerm = 'john';
      const mockResponse = {
        status_code: 404,
        data: []
      };

      service.searchDrivers(searchTerm).subscribe(result => {
        expect(result).toEqual([]);
        done();
      });

      const req = httpMock.expectOne(`admin/drivers?search=${searchTerm}&status=ACTIVE&dump=1&page_size=50`);
      req.flush(mockResponse);
    });
  });
});
