import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { of } from 'rxjs';

import { NotificationService } from './notification.service';
import { AuthService } from '@modules/auth/services/auth.service';
import { UtilsService, DataService } from '@core/services';
import { MockAuthService, MockUtilsService, MockDataService, createMockUser } from '../../../testing/test-utils';

describe('NotificationService', () => {
  let service: NotificationService;
  let httpMock: HttpTestingController;
  let mockAuthService: MockAuthService;
  let mockUtilsService: MockUtilsService;
  let mockDataService: MockDataService;

  beforeEach(() => {
    mockAuthService = new MockAuthService();
    mockUtilsService = new MockUtilsService();
    mockDataService = new MockDataService();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        NotificationService,
        { provide: AuthService, useValue: mockAuthService },
        { provide: UtilsService, useValue: mockUtilsService },
        { provide: DataService, useValue: mockDataService }
      ]
    });

    service = TestBed.inject(NotificationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('observables', () => {
    it('should provide unread notification observable', (done) => {
      service.unreadNotification$.subscribe(hasNew => {
        expect(typeof hasNew).toBe('boolean');
        done();
      });
    });

    it('should provide notification stream observable', (done) => {
      service.notifications$.subscribe(notifications => {
        expect(Array.isArray(notifications) || notifications === null).toBe(true);
        done();
      });
    });
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      expect(service.totalCount).toBe(0);
      expect(service.notificationsOpened).toBe(false);
    });

    it('should initialize notification stream for admin user', () => {
      const adminUser = createMockUser({ type: 'ADMIN' });
      mockAuthService.setUserData(adminUser);

      service.triggerNotificationLoad(1);

      expect(service).toBeTruthy();
    });
  });

  describe('readNotification', () => {
    it('should mark notification as read successfully', async () => {
      const mockNotification = {
        id: 123,
        read: false,
        key: 'TEST',
        subject: 'Test notification',
        body: 'Test body',
        status: 'UNREAD',
        created: new Date(),
        reservation_id: 456
      };

      const mockResponse = {
        status_code: 200,
        data: { id: mockNotification.id, read: true }
      };

      mockDataService.get.and.returnValue(Promise.resolve(mockResponse));

      await service.readNotification(mockNotification);

      expect(mockDataService.get).toHaveBeenCalledWith(`notifications/${mockNotification.id}/read`);
    });

    it('should handle already read notification', async () => {
      const mockNotification = {
        id: 123,
        read: true,
        key: 'TEST',
        subject: 'Test notification',
        body: 'Test body',
        status: 'READ',
        created: new Date(),
        reservation_id: 456
      };

      spyOn(service as any, 'openNotification');

      await service.readNotification(mockNotification);

      expect(service['openNotification']).toHaveBeenCalledWith(mockNotification);
    });
  });

  describe('notification state management', () => {
    it('should track notifications opened state', () => {
      expect(service.notificationsOpened).toBe(false);

      service.notificationsOpened = true;
      expect(service.notificationsOpened).toBe(true);
    });

    it('should update total count', () => {
      const initialCount = service.totalCount;
      const newCount = 10;

      service.totalCount = newCount;
      expect(service.totalCount).toBe(newCount);
    });
  });
});
