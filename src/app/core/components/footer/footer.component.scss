footer {
    width: 100%;
    bottom: 0;
    padding: 10px 5%;
    background: #fff;
    border-top: 1px solid;
    .container {
      padding-top: 8px;
      border-top: 1px solid black;
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        color: black;
      }
      .social-media {
        width: 120px;
      }
    }
  button.mat-focus-indicator.icon-button.mat-stroked-button.mat-button-base {
    border: 1px solid;
    margin: 0 4px;
    padding: 0;
    min-width: 35px;
    border-radius: 7px;
  }
}
