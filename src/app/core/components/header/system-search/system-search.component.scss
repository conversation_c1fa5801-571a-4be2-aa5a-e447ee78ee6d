.system-search {
  ::ng-deep {
    .mat-form-field-appearance-outline .mdc-text-field--outlined .mdc-notched-outline__leading {
      border-bottom-left-radius: 0 !important;
    }

    .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
      border-bottom-right-radius: 0 !important;
    }

    .mdc-text-field__input {
      &::placeholder {
        font-size: 12px;
        font-weight: 500;
        color: #808FA4;
      }
    }

    .mat-mdc-form-field-icon-suffix {
      font-size: 12px;
      font-weight: 500;
      color: #808FA4;

      > span {
        display: inline-flex !important;
        justify-content: center;
        align-items: center;
        gap: 1rem;
      }
    }
  }

  .search-results {
    padding: 1rem 0.75rem;

    .search-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      cursor: pointer;
      padding: 0.75rem;
      border-radius: 8px;
      transition: background-color ease-in 100ms;

      &:hover {
        background-color: #DFE9F5;
      }

      > h4 {
        margin-bottom: 0;
        font-weight: 500;
        color: #363F45;
      }

      .category-item {
        font-size: 12px;
        font-weight: 500;
        color: #808FA4;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .search-placeholder {
    text-align: center;

    > h4 {
      font-weight: 700;
    }

    > p {
      font-weight: 500;
      color: #808FA4;
    }
  }
}
