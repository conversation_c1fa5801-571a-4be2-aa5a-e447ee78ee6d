.envoy-logo {
  display: flex;
  align-items: center;
}

.nav-menu, .actions-center {
  align-items: center;
  display: none;
  gap: 0.25rem;

  .nav-item, .action-item {
    --mdc-text-button-container-shape: 16px;
    padding: 12px 16px 12px 12px;

    &__active {
      --mdc-text-button-label-text-size: 16px;
      --mdc-text-button-label-text-color: #007CC4 !important;
      --mdc-text-button-label-text-weight: 600 !important;
      background-color: #E6F4FC;
    }
  }

  ::ng-deep {
    .nav-item {
      @media screen and (min-width: 768px) and (max-width: 1199px) {
        &__active .mdc-button__label {
          max-width: unset !important;
        }

        &:not(&__active):not(:hover) .mat-icon {
          transition: width 300ms linear;
          width: 1.3rem;
        }

        .mdc-button__label {
          transition: max-width 300ms linear;
          display: inline-block;
          max-width: 115px;
        }

        &:not(:hover) .mdc-button__label {
          overflow: hidden;
          max-width: 0;
        }
      }
    }

    .action-item {
      min-width: 46px;
      padding: 0 8px !important;

      &__active .mdc-button__label {
        max-width: unset !important;
      }

      .mdc-button__label {
        transition: max-width 300ms linear;
        display: inline-block;
        max-width: 115px;
      }

      &:not(:hover) {
        &:not(.action-item__active) .mat-icon {
          margin-right: 0 !important;
        }

        .mdc-button__label {
          overflow: hidden;
          max-width: 0;
        }
      }
    }
  }
}

::ng-deep {
  .notification-menu {
    min-width: 400px !important;
    border-radius: 12px !important;
    --mat-menu-item-label-text-color: #363F45;

    .mat-mdc-menu-content {
      max-height: 80vh;
    }

    &__header {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      border-bottom: 1px solid #DFE9F5;
      margin-bottom: 0.5rem;
      font-size: 18px;
      font-weight: 500;
      gap: 0.75rem;

      > .mat-icon > svg path {
        fill: #000000;
      }
    }

    &__content {
      height: 400px;
      //height: calc(80vh - 41px);
      overflow-y: scroll;
    }

    .no-notifications {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0.5rem 1rem;
      align-items: center;
      min-height: 300px;
      color: #808fa4;
      gap: 1rem;

      > .mat-icon {
        width: 50%;
        height: 50%;
      }
    }

    .mat-mdc-menu-item {
      align-items: flex-start;

      &-text {
        flex: 1
      }

      .mat-icon {
        width: 40px;
        height: 40px;
        margin-top: 10px;
      }

      &.read {
        opacity: 70%;

        .notification-title > h3 {
          font-weight: 500;
        }
      }
    }

    .notification-title {
      display: flex;
      justify-content: space-between;

      > h3 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0;
      }

      > span {
        font-size: 12px;
        color: #627186;
      }
    }

    .content {
      font-size: 14px;
      font-weight: 500;
      color: #808FA4;
    }
  }

  .profile-mat-menu, .mobile-nav-menu {
    min-width: 160px !important;
    border-radius: 12px !important;
    --mat-menu-item-label-text-color: #363F45;

    .mat-mdc-menu-item {
      --mat-menu-item-label-text-weight: 500;
      --mat-menu-item-label-text-size: 15px;
      min-height: 40px;
    }
  }

  .mobile-nav-menu {
    min-width: 220px !important;

    .active-item {
      background-color: #E6F4FC;
      --mat-menu-item-label-text-size: 16px;
      --mat-menu-item-label-text-weight: 600;
      --mat-menu-item-label-text-color: #007cc4;
    }
  }
}
