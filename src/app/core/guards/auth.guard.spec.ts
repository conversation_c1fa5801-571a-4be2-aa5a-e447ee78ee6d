import { TestBed } from '@angular/core/testing';
import { Route, UrlSegment } from '@angular/router';

import { authGuard } from './auth.guard';
import { AuthService } from '@modules/auth/services/auth.service';
import { MockAuthService, createMockUser } from '../../testing/test-utils';

describe('authGuard', () => {
  let mockAuthService: MockAuthService;

  beforeEach(() => {
    mockAuthService = new MockAuthService();

    TestBed.configureTestingModule({
      providers: [
        { provide: AuthService, useValue: mockAuthService }
      ]
    });
  });

  it('should be created', () => {
    expect(authGuard).toBeTruthy();
  });

  describe('canMatch', () => {
    let mockRoute: Route;
    let mockSegments: UrlSegment[];

    beforeEach(() => {
      mockRoute = { path: 'dashboard' };
      mockSegments = [];
    });

    it('should allow access when user is authenticated', () => {
      mockAuthService.isAuthenticated.and.returnValue(true);

      const result = TestBed.runInInjectionContext(() =>
        authGuard(mockRoute, mockSegments)
      );

      expect(result).toBe(true);
    });

    it('should deny access when user is not authenticated', async () => {
      mockAuthService.isAuthenticated.and.returnValue(false);
      mockAuthService.logout.and.returnValue(Promise.resolve());

      const result = await TestBed.runInInjectionContext(() =>
        authGuard(mockRoute, mockSegments)
      );

      expect(result).toBe(false);
      expect(mockAuthService.logout).toHaveBeenCalled();
    });

    it('should handle logout promise rejection', async () => {
      mockAuthService.isAuthenticated.and.returnValue(false);
      mockAuthService.logout.and.returnValue(Promise.reject('error'));

      try {
        await TestBed.runInInjectionContext(() =>
          authGuard(mockRoute, mockSegments)
        );
      } catch (error) {
        expect(error).toBe('error');
      }
    });
  });

  describe('authentication scenarios', () => {
    it('should handle valid user authentication', () => {
      const mockUser = createMockUser();
      mockAuthService.setUserData(mockUser);
      mockAuthService.isAuthenticated.and.returnValue(true);

      const result = TestBed.runInInjectionContext(() =>
        authGuard({ path: 'protected' }, [])
      );

      expect(result).toBe(true);
    });

    it('should handle null user data', async () => {
      mockAuthService.setUserData(null);
      mockAuthService.isAuthenticated.and.returnValue(false);
      mockAuthService.logout.and.returnValue(Promise.resolve());

      const result = await TestBed.runInInjectionContext(() =>
        authGuard({ path: 'protected' }, [])
      );

      expect(result).toBe(false);
    });

    it('should handle expired tokens', async () => {
      mockAuthService.isAuthenticated.and.returnValue(false);
      mockAuthService.logout.and.returnValue(Promise.resolve());

      const result = await TestBed.runInInjectionContext(() =>
        authGuard({ path: 'protected' }, [])
      );

      expect(result).toBe(false);
      expect(mockAuthService.logout).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle authentication service errors gracefully', async () => {
      mockAuthService.isAuthenticated.and.throwError('Auth service error');

      expect(() => {
        TestBed.runInInjectionContext(() =>
          authGuard({ path: 'protected' }, [])
        );
      }).toThrow('Auth service error');
    });

    it('should handle logout errors', async () => {
      mockAuthService.isAuthenticated.and.returnValue(false);
      mockAuthService.logout.and.returnValue(Promise.reject('Logout failed'));

      try {
        await TestBed.runInInjectionContext(() =>
          authGuard({ path: 'protected' }, [])
        );
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBe('Logout failed');
      }
    });
  });

  describe('route handling', () => {
    it('should work with different route configurations', () => {
      mockAuthService.isAuthenticated.and.returnValue(true);

      const routes = [
        { path: 'dashboard' },
        { path: 'admin' },
        { path: 'settings' }
      ];

      routes.forEach(route => {
        const result = TestBed.runInInjectionContext(() =>
          authGuard(route, [])
        );
        expect(result).toBe(true);
      });
    });

    it('should handle empty route configuration', () => {
      mockAuthService.isAuthenticated.and.returnValue(true);

      const result = TestBed.runInInjectionContext(() =>
        authGuard({}, [])
      );

      expect(result).toBe(true);
    });
  });
});
