import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule, FormControl, NG_VALUE_ACCESSOR, NG_VALIDATORS, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatOptionModule } from '@angular/material/core';

import { UserAddressComponent } from './user-address.component';

describe('UserAddressComponent', () => {
  let component: UserAddressComponent;
  let fixture: ComponentFixture<UserAddressComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        UserAddressComponent,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatIconModule,
        MatOptionModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UserAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should implement ControlValueAccessor', () => {
    expect(component.writeValue).toBeDefined();
    expect(component.registerOnChange).toBeDefined();
    expect(component.registerOnTouched).toBeDefined();
  });

  it('should validate address fields', () => {
    const control = new FormControl();
    const result = component.validate(control);
    expect(result).toBeDefined();
  });

  it('should handle address input', () => {
    const testAddress = {
      type: 'HOME',
      street_1: '123 Main St',
      street_2: 'Apt 4B',
      country: 'USA',
      region: 'CA',
      city: 'Test City',
      postal_code: '12345'
    };

    component.writeValue(testAddress);
    expect(component.formGroup.get('type')?.value).toBe('HOME');
    expect(component.formGroup.get('street_1')?.value).toBe('123 Main St');
    expect(component.formGroup.get('street_2')?.value).toBe('Apt 4B');
    expect(component.formGroup.get('country')?.value).toBe('USA');
    expect(component.formGroup.get('region')?.value).toBe('CA');
    expect(component.formGroup.get('city')?.value).toBe('Test City');
    expect(component.formGroup.get('postal_code')?.value).toBe('12345');
  });

  it('should validate required fields', () => {
    const form = component.formGroup;
    expect(form.valid).toBeFalsy();

    form.patchValue({
      type: 'HOME',
      street_1: '123 Main St',
      country: 'USA',
      region: 'CA',
      city: 'Test City',
      postal_code: '12345'
    });

    expect(form.valid).toBeTruthy();
  });

  it('should handle address type input', () => {
    component.hasAddressType = false;
    component.ngOnInit();
    
    expect(component.formGroup.get('type')?.disabled).toBeTruthy();
    expect(component.formGroup.get('type')?.hasValidator(Validators.required)).toBeFalsy();
  });

  it('should validate street length', () => {
    const streetControl = component.formGroup.get('street_1');
    
    streetControl?.setValue('ab');
    expect(streetControl?.errors?.['minlength']).toBeTruthy();

    streetControl?.setValue('a'.repeat(256));
    expect(streetControl?.errors?.['maxlength']).toBeTruthy();

    streetControl?.setValue('123 Main St');
    expect(streetControl?.errors).toBeNull();
  });

  it('should validate city length', () => {
    const cityControl = component.formGroup.get('city');
    
    cityControl?.setValue('ab');
    expect(cityControl?.errors?.['minlength']).toBeTruthy();

    cityControl?.setValue('a'.repeat(256));
    expect(cityControl?.errors?.['maxlength']).toBeTruthy();

    cityControl?.setValue('Test City');
    expect(cityControl?.errors).toBeNull();
  });
});
