import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';

import { ResetPasswordComponent } from './reset-password.component';
import { DataService, UtilsService } from '@core/services';
import { MockUtilsService } from '../../../testing/test-utils';

describe('ResetPasswordComponent', () => {
  let component: ResetPasswordComponent;
  let fixture: ComponentFixture<ResetPasswordComponent>;
  let mockDataService: jasmine.SpyObj<DataService>;
  let mockUtilsService: MockUtilsService;

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['put']);
    mockUtilsService = new MockUtilsService();

    await TestBed.configureTestingModule({
      imports: [
        ResetPasswordComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule
      ],
      providers: [
        { provide: DataService, useValue: dataServiceSpy },
        { provide: UtilsService, useValue: mockUtilsService },
        {
          provide: MatDialogRef,
          useValue: {
            close: jasmine.createSpy('close')
          }
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { userId: 1, endpoint: 'admin/users' }
        }
      ]
    })
    .compileComponents();

    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockDataService.put.and.returnValue(Promise.resolve({ status_code: 200 }));

    fixture = TestBed.createComponent(ResetPasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty password fields', () => {
    expect(component.formGroup.get('password')?.value).toBe('');
    expect(component.formGroup.get('confirm_password')?.value).toBe('');
  });

  it('should validate required fields', () => {
    component.formGroup.patchValue({ password: '', confirm_password: '' });
    expect(component.formGroup.valid).toBeFalsy();

    component.formGroup.patchValue({ password: 'test123', confirm_password: 'test123' });
    expect(component.formGroup.valid).toBeTruthy();
  });

  it('should validate password match', () => {
    component.formGroup.patchValue({ password: 'test123', confirm_password: 'test456' });
    expect(component.formGroup.valid).toBeFalsy();
    expect(component.formGroup.get('confirm_password')?.errors?.['mismatch']).toBeTruthy();

    component.formGroup.patchValue({ password: 'test123', confirm_password: 'test123' });
    expect(component.formGroup.valid).toBeTruthy();
    expect(component.formGroup.get('confirm_password')?.errors?.['mismatch']).toBeFalsy();
  });

  it('should call changePassword and close dialog on success', async () => {
    component.formGroup.patchValue({ password: 'test123', confirm_password: 'test123' });
    await component.changePassword();
    
    expect(mockDataService.put).toHaveBeenCalledWith('admin/users/1', {
      password: 'test123',
      confirm_password: 'test123'
    });
    expect(TestBed.inject(MatDialogRef).close).toHaveBeenCalled();
  });
});
